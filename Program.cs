using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using HealthStatus = ZeroDateStrat.Models.HealthStatus;
using Serilog;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ZeroDateStrat.Strategies;
using ZeroDateStrat.Utils;

namespace ZeroDateStrat;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine($"DEBUG: Main method called with {args.Length} arguments");
        if (args.Length > 0)
        {
            Console.WriteLine($"DEBUG: First argument: '{args[0]}'");
        }

        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/zerodtestrat-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        try
        {
            // Initialize global exception handler early (without Discord for early initialization)
            var tempServiceProvider = new ServiceCollection()
                .AddLogging(builder => builder.AddSerilog())
                .AddSingleton<IGlobalExceptionHandler>(provider =>
                {
                    var logger = provider.GetRequiredService<ILogger<GlobalExceptionHandler>>();
                    return new GlobalExceptionHandler(logger, null); // No Discord service during early init
                })
                .BuildServiceProvider();

            var globalExceptionHandler = tempServiceProvider.GetRequiredService<IGlobalExceptionHandler>();

            // Check if running in test mode
            if (args.Length > 0 && args[0].ToLower() == "test")
            {
                Log.Information("Running in test mode");
                await globalExceptionHandler.ExecuteWithRetryAsync(async () =>
                {
                    ZeroDateStrat.Tests.BasicTests.RunAllTests();
                }, 1, "BasicTests");
                return;
            }

            // Check if running Discord error notification test
            if (args.Length > 0 && args[0].ToLower() == "discord-error-test")
            {
                Log.Information("Running Discord error notification test");
                await ZeroDateStrat.Tests.BasicTests.RunDiscordErrorTestAsync();
                return;
            }

            // Check if running guidance request test
            if (args.Length > 0 && args[0].ToLower() == "guidance-test")
            {
                Log.Information("Running Discord guidance request test");
                await ZeroDateStrat.Tests.GuidanceRequestTest.RunGuidanceRequestTestAsync();
                return;
            }

            // Check if running ChatGPT bot test
            if (args.Length > 0 && args[0].ToLower() == "chatgpt-test")
            {
                Console.WriteLine("DEBUG: ChatGPT test argument detected");
                Log.Information("Running ChatGPT bot test");
                Console.WriteLine("DEBUG: About to call ChatGPTBotTest.RunAsync()");
                await ZeroDateStrat.Tests.ChatGPTBotTest.RunAsync();
                Console.WriteLine("DEBUG: ChatGPTBotTest.RunAsync() completed");
                return;
            }

            // Check if running comprehensive Discord test
            if (args.Length > 0 && args[0].ToLower() == "discord-test-all")
            {
                Log.Information("Running comprehensive Discord message test suite");
                await ZeroDateStrat.Tests.BasicTests.RunComprehensiveDiscordTestAsync();
                return;
            }

            // Check if running integration test
            if (args.Length > 0 && args[0].ToLower() == "integration")
            {
                Log.Information("Running integration test mode");
                await ZeroDateStrat.Tests.OptionsDataIntegrationTest.RunIntegrationTest();
                return;
            }

            // Check if running backtesting test
            if (args.Length > 0 && args[0].ToLower() == "backtest")
            {
                Log.Information("Running backtesting framework test mode");
                await ZeroDateStrat.Tests.BacktestingFrameworkTest.RunBacktestingFrameworkTest();
                return;
            }

            // Check if running debug test
            if (args.Length > 0 && args[0].ToLower() == "debug")
            {
                Log.Information("Running debug test mode");
                await ZeroDateStrat.Tests.DebugBacktestTest.RunDebugTest();
                return;
            }

            // Check if running exit analysis test
            if (args.Length > 0 && args[0].ToLower() == "exit")
            {
                Log.Information("Running exit analysis test mode");
                await ZeroDateStrat.Tests.TradeExitAnalysisTest.RunExitAnalysisTest();
                return;
            }

            // Check if running enhanced risk management test
            if (args.Length > 0 && args[0].ToLower() == "risk")
            {
                Log.Information("Running enhanced risk management test mode");
                await ZeroDateStrat.Tests.EnhancedRiskManagementTest.RunEnhancedRiskManagementTest();
                return;
            }

            // Check if running Phase 3 demo
            if (args.Length > 0 && args[0].ToLower() == "phase3")
            {
                Log.Information("Running Phase 3 demo mode");
                await ZeroDateStrat.Tests.Phase3Demo.RunPhase3Demo();
                return;
            }

            // Check if running Polygon VIX integration test
            if (args.Length > 0 && args[0].ToLower() == "polygon")
            {
                Log.Information("Running Polygon VIX integration test mode");
                await ZeroDateStrat.Tests.PolygonVixIntegrationTest.RunPolygonVixIntegrationTest();
                return;
            }

            // Check if running Alpaca VIX integration test
            if (args.Length > 0 && args[0].ToLower() == "alpacavix")
            {
                Log.Information("Running Alpaca VIX integration test mode");
                await ZeroDateStrat.Tests.AlpacaVixIntegrationTest.RunAlpacaVixIntegrationTest();
                return;
            }

            // Check if running SyntheticVIX service test
            if (args.Length > 0 && args[0].ToLower() == "syntheticvix")
            {
                Log.Information("Running SyntheticVIX service test mode");
                await ZeroDateStrat.Tests.SyntheticVixServiceTest.RunTestAsync();
                return;
            }

            // Check if running SyntheticVIX analytics test
            if (args.Length > 0 && args[0].ToLower() == "syntheticvix-analytics")
            {
                Log.Information("Running SyntheticVIX analytics test mode");
                await ZeroDateStrat.Tests.SyntheticVixAnalyticsTest.RunTestAsync();
                return;
            }

            // Check if running quick SyntheticVIX test
            if (args.Length > 0 && args[0].ToLower() == "quickvix")
            {
                Log.Information("Running quick SyntheticVIX test mode");
                await ZeroDateStrat.Tests.QuickSyntheticVixTest.RunQuickTestAsync();
                return;
            }

            // Check if running comprehensive SyntheticVIX analytics test
            if (args.Length > 0 && args[0].ToLower() == "comprehensive-vix")
            {
                Log.Information("Running comprehensive SyntheticVIX analytics test mode");
                await ZeroDateStrat.Tests.ComprehensiveSyntheticVixAnalyticsTest.RunComprehensiveTestAsync();
                return;
            }

            // Check if running enhanced options pricing test
            if (args.Length > 0 && args[0].ToLower() == "pricing")
            {
                Log.Information("Running enhanced options pricing test mode");
                await ZeroDateStrat.Tests.EnhancedOptionsPricingTest.RunEnhancedOptionsPricingTest();
                return;
            }

            // Check if running account info test
            if (args.Length > 0 && args[0].ToLower() == "account")
            {
                Log.Information("Running account info test mode");
                await ZeroDateStrat.Tests.AccountInfoTest.RunAccountInfoTest();
                return;
            }

            // Check if running Phase 2 integration test
            if (args.Length > 0 && args[0].ToLower() == "phase2")
            {
                Log.Information("Running Phase 2 integration test mode");
                await ZeroDateStrat.Tests.Phase2IntegrationTest.RunPhase2IntegrationTest();
                return;
            }

            // Check if running enhanced market analysis test
            if (args.Length > 0 && args[0].ToLower() == "market")
            {
                Log.Information("Running enhanced market analysis test mode");
                await ZeroDateStrat.Tests.EnhancedMarketAnalysisTest.RunEnhancedMarketAnalysisTest();
                return;
            }

            // Check if running data integration fix test
            if (args.Length > 0 && args[0].ToLower() == "datafix")
            {
                Log.Information("Running simple data integration fix test mode");
                await ZeroDateStrat.Tests.SimpleDataFixTest.RunSimpleDataFixTest();
                return;
            }

            // Check if running Priority 1 infrastructure test
            if (args.Length > 0 && args[0].ToLower() == "priority1")
            {
                Log.Information("Running Priority 1 infrastructure test mode");
                await ZeroDateStrat.Tests.Priority1InfrastructureTest.RunPriority1InfrastructureTest();
                return;
            }

            // Check if running enhanced monitoring test
            if (args.Length > 0 && args[0].ToLower() == "monitoring")
            {
                Log.Information("Running enhanced monitoring test mode");
                await ZeroDateStrat.Tests.EnhancedMonitoringTest.RunEnhancedMonitoringTestStatic();
                return;
            }

            // Check if running notification system test
            if (args.Length > 0 && args[0].ToLower() == "notifications")
            {
                Log.Information("Running notification system test mode");
                await ZeroDateStrat.Tests.NotificationSystemTest.RunNotificationSystemTest();
                return;
            }

            // Check if running Discord integration test
            if (args.Length > 0 && args[0].ToLower() == "discord")
            {
                Log.Information("Running Discord integration test mode");
                await ZeroDateStrat.Tests.DiscordIntegrationTest.RunDiscordIntegrationTest();
                return;
            }

            // Check if running Discord live test
            if (args.Length > 0 && args[0].ToLower() == "discordlive")
            {
                Log.Information("Running Discord live test mode");
                await ZeroDateStrat.Tests.DiscordLiveTest.RunDiscordLiveTest();
                return;
            }

            // Check if running Discord simple test
            if (args.Length > 0 && args[0].ToLower() == "discordsimple")
            {
                Log.Information("Running Discord simple test mode");
                await ZeroDateStrat.Tests.DiscordSimpleTest.RunDiscordSimpleTest();
                return;
            }

            // Check if running Priority 1 & 2 enhancements test
            if (args.Length > 0 && args[0].ToLower() == "enhancements")
            {
                Log.Information("Running Priority 1 & 2 enhancements test mode");
                await ZeroDateStrat.Tests.Priority1And2EnhancementsTest.RunPriority1And2EnhancementsTest();
                return;
            }

            // Check if running comprehensive enhancements test
            if (args.Length > 0 && args[0].ToLower() == "comprehensive")
            {
                Log.Information("Running comprehensive enhancements test mode");
                await ZeroDateStrat.Tests.ComprehensiveEnhancementsTest.RunComprehensiveEnhancementsTest();
                return;
            }

            // Check if running Polygon options data test
            if (args.Length > 0 && args[0].ToLower() == "polygon")
            {
                Log.Information("Running Polygon.io options data test mode");
                await ZeroDateStrat.Tests.PolygonOptionsDataTest.RunPolygonOptionsDataTest();
                return;
            }

            // Check if running Polygon endpoint test
            if (args.Length > 0 && args[0].ToLower() == "endpoints")
            {
                Log.Information("Running Polygon.io endpoint test mode");
                await ZeroDateStrat.Tests.PolygonEndpointTest.RunPolygonEndpointTest();
                return;
            }

            // Check if running options chain integration test
            if (args.Length > 0 && args[0].ToLower() == "options-chain")
            {
                Log.Information("Running options chain integration test mode");
                await ZeroDateStrat.Tests.OptionsChainIntegrationTest.RunOptionsChainIntegrationTest();
                return;
            }

            // Check if running comprehensive Polygon options endpoint test
            if (args.Length > 0 && args[0].ToLower() == "options-endpoints")
            {
                Log.Information("Running comprehensive Polygon.io options endpoint test mode");
                await ZeroDateStrat.Tests.PolygonOptionsEndpointTest.RunPolygonOptionsEndpointTest();
                return;
            }

            // Check if running safety validation
            if (args.Length > 0 && args[0].ToLower() == "safety")
            {
                Log.Information("Running comprehensive safety validation");
                await RunSafetyValidation();
                return;
            }

            // Check if running emergency stop
            if (args.Length > 0 && args[0].ToLower() == "emergency")
            {
                Log.Warning("Running emergency stop procedure");
                await EmergencyStop.ExecuteEmergencyStopAsync(args);
                return;
            }

            Log.Information("Starting Zero DTE Trading Application");

            var host = CreateHostBuilder(args).Build();

            // Get services
            var configValidator = host.Services.GetRequiredService<IConfigurationValidator>();
            var securityService = host.Services.GetRequiredService<ISecurityService>();
            var hostExceptionHandler = host.Services.GetRequiredService<IGlobalExceptionHandler>();
            var alpacaService = host.Services.GetRequiredService<IAlpacaService>();
            var strategy = host.Services.GetRequiredService<IZeroDteStrategy>();
            var logger = host.Services.GetRequiredService<ILogger<Program>>();
            var discordService = host.Services.GetRequiredService<IDiscordService>();
            var discordCommandHandler = host.Services.GetRequiredService<IDiscordCommandHandler>();

            // Validate configuration
            logger.LogInformation("Validating application configuration...");
            var configValidation = await configValidator.ValidateConfigurationAsync();
            if (!configValidation.IsValid)
            {
                logger.LogError("Configuration validation failed. Cannot start application.");
                foreach (var error in configValidation.Errors)
                {
                    logger.LogError("Configuration Error: {Error}", error);
                }
                return;
            }

            // Perform security audit
            logger.LogInformation("Performing security audit...");
            var securityAudit = await securityService.PerformSecurityAuditAsync();
            if (!securityAudit.IsSecure)
            {
                logger.LogWarning("Security audit indicates potential issues. Score: {Score:P0}",
                                securityAudit.OverallSecurityScore);
                foreach (var check in securityAudit.Checks.Where(c => !c.Passed))
                {
                    logger.LogWarning("Security Issue: {CheckName} - {Message}", check.CheckName, check.Message);
                }
            }

            // Initialize Alpaca connection with retry
            logger.LogInformation("Initializing Alpaca connection...");
            var alpacaInitialized = await hostExceptionHandler.ExecuteWithRetryAsync(async () =>
            {
                return await alpacaService.InitializeAsync();
            }, 3, "AlpacaInitialization");

            if (alpacaInitialized != true)
            {
                logger.LogError("Failed to initialize Alpaca service after retries");
                return;
            }

            logger.LogInformation("Alpaca connection established successfully");

            // Setup Discord command handling
            logger.LogInformation("Setting up Discord command handling...");
            discordService.OnCommandReceived += discordCommandHandler.HandleCommandAsync;
            discordService.OnMessageReceived += discordCommandHandler.HandleMessageAsync;

            // Start the trading loop with error handling
            await hostExceptionHandler.ExecuteWithRetryAsync(async () =>
            {
                await RunTradingLoop(strategy, logger, hostExceptionHandler);
            }, 1, "TradingLoop");
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Application terminated unexpectedly");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddEnvironmentVariables();
                config.AddCommandLine(args);
            })
            .ConfigureServices((context, services) =>
            {
                // Configuration validation
                services.AddOptions<TradingConfiguration>()
                    .Bind(context.Configuration.GetSection("Trading"))
                    .ValidateDataAnnotations()
                    .ValidateOnStart();

                services.AddOptions<AlpacaConfiguration>()
                    .Bind(context.Configuration.GetSection("Alpaca"))
                    .ValidateDataAnnotations()
                    .ValidateOnStart();

                services.AddOptions<RiskConfiguration>()
                    .Bind(context.Configuration.GetSection("Risk"))
                    .ValidateDataAnnotations()
                    .ValidateOnStart();

                // Performance Services
                services.AddSingleton<IHighPerformanceCacheService, HighPerformanceCacheService>();
                services.AddSingleton<IPerformanceMonitoringService, PerformanceMonitoringService>();

                // Advanced Cache Services
                services.AddSingleton<ICacheAnalyticsService, CacheAnalyticsService>();
                services.AddSingleton<IPredictiveCachingService, PredictiveCachingService>();
                services.AddSingleton<ICacheWarmingService, CacheWarmingService>();

                // Register cache warming as hosted service
                services.AddHostedService<CacheWarmingService>();
                services.AddHostedService<PredictiveCachingService>();
                services.AddHostedService<CacheAnalyticsService>();

                // Service to wire up cache dependencies after DI container is built
                services.AddHostedService<CacheServiceInitializer>();

                // Core Services
                services.AddSingleton<IPolygonDataService, PolygonDataService>();
                services.AddSingleton<IOptionsPricingService, OptionsPricingService>();
                services.AddSingleton<IOptionsChainService, OptionsChainService>();
                services.AddSingleton<IAlpacaService, AlpacaService>();
                services.AddSingleton<ISyntheticVixAnalyticsService, SyntheticVixAnalyticsService>();
                services.AddSingleton<ISyntheticVixDashboardService, SyntheticVixDashboardService>();
                services.AddSingleton<ISyntheticVixService, SyntheticVixService>();
                services.AddSingleton<IAlpacaVixService, AlpacaVixService>();
                services.AddSingleton<IMarketRegimeAnalyzer, MarketRegimeAnalyzer>();
                services.AddSingleton<ITradingCalendarService, TradingCalendarService>();
                services.AddSingleton<IOptionsScanner, OptionsScanner>();
                services.AddSingleton<IRiskManager, RiskManager>();
                services.AddSingleton<IEnhancedRiskManager, EnhancedRiskManager>();
                services.AddSingleton<IZeroDteStrategy, ZeroDteStrategy>();

                // Phase 2: Advanced Trading Services
                services.AddSingleton<IAdvancedRiskManager, AdvancedRiskManager>();
                services.AddSingleton<IPositionManager, PositionManager>();

                // Backtesting Framework Services
                services.AddSingleton<IHistoricalDataService, HistoricalDataService>();
                services.AddSingleton<IBacktestingEngine, BacktestingEngine>();
                services.AddSingleton<IPerformanceAnalytics, PerformanceAnalytics>();

                // Phase 3: Advanced Services
                services.AddSingleton<IMachineLearningService, MachineLearningService>();
                services.AddSingleton<IRealTimeMonitoringService, RealTimeMonitoringService>();
                services.AddSingleton<IAdvancedStrategyOptimizer, AdvancedStrategyOptimizer>();
                services.AddSingleton<IProductionInfrastructureService, ProductionInfrastructureService>();

                // Security Services
                services.AddSingleton<ISecurityService, SecurityService>();
                services.AddSingleton<IConfigurationValidator, ConfigurationValidator>();

                // Notification Services
                services.AddSingleton<INotificationService, NotificationService>();

                // Discord Services
                services.AddSingleton<IDiscordService, DiscordService>();
                services.AddSingleton<IDiscordCommandHandler, DiscordCommandHandler>();
                services.AddSingleton<ITradingNotificationService, TradingNotificationService>();
                services.AddSingleton<IGuidanceRequestService, GuidanceRequestService>();
                services.AddHostedService<DiscordService>();
                services.AddHostedService<TradingSchedulerService>();

                // OpenAI and ChatGPT Services
                services.AddHttpClient<IOpenAIService, OpenAIService>();
                services.AddSingleton<IChatGPTBotHandler, ChatGPTBotHandler>();

                // Add HttpClient for external services
                services.AddHttpClient<IPolygonDataService, PolygonDataService>();

                // Add high-performance memory caching optimized for i9-12900K + 32GB RAM
                services.AddMemoryCache(options =>
                {
                    // Aggressive caching for 32GB RAM system - use 6GB (18.75% of total RAM)
                    options.SizeLimit = 1024L * 1024L * 1024L * 6; // 6GB cache limit
                    options.CompactionPercentage = 0.15; // Compact when 85% full (more aggressive)
                    options.ExpirationScanFrequency = TimeSpan.FromMinutes(2); // More frequent cleanup
                });

                // Add health checks optimized for high-performance trading
                services.AddHealthChecks()
                    .AddCheck<TradingSystemHealthCheck>("trading_system", Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Degraded, new[] { "trading", "system" })
                    .AddCheck<MemoryHealthCheck>("memory", Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Degraded, new[] { "memory", "performance" });

                // Add logging
                services.AddLogging(builder =>
                {
                    builder.ClearProviders();
                    builder.AddSerilog();
                });

                // Add global exception handling with Discord integration
                services.AddSingleton<IGlobalExceptionHandler>(provider =>
                {
                    var logger = provider.GetRequiredService<ILogger<GlobalExceptionHandler>>();
                    var discordService = provider.GetService<IDiscordService>();
                    return new GlobalExceptionHandler(logger, discordService);
                });

                // Add safety validation
                services.AddSingleton<SafetyValidator>();
            });

    static async Task RunTradingLoop(IZeroDteStrategy strategy, ILogger<Program> logger, IGlobalExceptionHandler exceptionHandler)
    {
        logger.LogInformation("Starting trading loop...");

        var cancellationTokenSource = new CancellationTokenSource();

        // Get notification service from DI container
        var host = CreateHostBuilder(new string[0]).Build();
        var notificationService = host.Services.GetService<ITradingNotificationService>();

        // Send startup notification
        if (notificationService != null)
        {
            await notificationService.SendSystemStartupNotificationAsync();
        }

        // Track if we've sent morning report today
        var lastMorningReportDate = DateTime.MinValue;
        var lastEndOfDayReportDate = DateTime.MinValue;

        // Handle Ctrl+C gracefully
        Console.CancelKeyPress += (sender, e) =>
        {
            e.Cancel = true;
            cancellationTokenSource.Cancel();
            logger.LogInformation("Shutdown requested...");
        };

        try
        {
            while (!cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    var now = DateTime.Now;

                    // Send morning report once per day at 8:30 AM ET
                    if (notificationService != null &&
                        now.TimeOfDay >= TimeSpan.FromHours(8.5) &&
                        now.TimeOfDay <= TimeSpan.FromHours(9) &&
                        lastMorningReportDate.Date != now.Date)
                    {
                        logger.LogInformation("Sending morning portfolio report");
                        await notificationService.SendMorningReportAsync();
                        lastMorningReportDate = now;
                    }

                    // Send end-of-day report once per day at 4:15 PM ET
                    if (notificationService != null &&
                        now.TimeOfDay >= TimeSpan.FromHours(16.25) &&
                        now.TimeOfDay <= TimeSpan.FromHours(16.5) &&
                        lastEndOfDayReportDate.Date != now.Date)
                    {
                        logger.LogInformation("Sending end-of-day trading report");
                        await notificationService.SendEndOfDayReportAsync();
                        lastEndOfDayReportDate = now;
                    }
                    // Check if we should trade with error handling
                    var shouldTrade = await exceptionHandler.ExecuteWithRetryAsync(async () =>
                    {
                        return await strategy.ShouldTrade();
                    }, 2, "ShouldTrade");

                    if (shouldTrade == true)
                    {
                        logger.LogInformation("Scanning for trading opportunities...");

                        // Generate trading signals with error handling
                        var signals = await exceptionHandler.ExecuteWithRetryAsync(async () =>
                        {
                            return await strategy.GenerateSignalsAsync();
                        }, 2, "GenerateSignals");

                        if (signals?.Any() == true)
                        {
                            logger.LogInformation($"Found {signals.Count} trading signals");

                            // Execute the best signals (limit to 3 per cycle)
                            var signalsToExecute = signals.Take(3);

                            foreach (var signal in signalsToExecute)
                            {
                                logger.LogInformation($"Executing signal: {signal.Strategy} for {signal.UnderlyingSymbol}");
                                logger.LogInformation($"  Expected Profit: {signal.ExpectedProfit:C2}");
                                logger.LogInformation($"  Max Loss: {signal.MaxLoss:C2}");
                                logger.LogInformation($"  Risk/Reward: {signal.RiskRewardRatio:F2}");
                                logger.LogInformation($"  Confidence: {signal.Confidence:P1}");

                                var success = await exceptionHandler.ExecuteWithRetryAsync(async () =>
                                {
                                    return await strategy.ExecuteSignalAsync(signal);
                                }, 1, $"ExecuteSignal-{signal.Id}");

                                if (success == true)
                                {
                                    logger.LogInformation($"Successfully executed signal {signal.Id}");
                                }
                                else
                                {
                                    logger.LogWarning($"Failed to execute signal {signal.Id}");
                                }

                                // Small delay between executions
                                await Task.Delay(1000, cancellationTokenSource.Token);
                            }
                        }
                        else
                        {
                            logger.LogInformation("No trading opportunities found");
                        }

                        // Manage existing positions with error handling
                        await exceptionHandler.ExecuteWithRetryAsync(async () =>
                        {
                            await strategy.ManagePositionsAsync();
                        }, 2, "ManagePositions");
                    }
                    else
                    {
                        logger.LogDebug("Not trading at this time");
                    }

                    // Wait before next scan (30 seconds)
                    await Task.Delay(30000, cancellationTokenSource.Token);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    await exceptionHandler.HandleExceptionAsync(ex, "TradingLoop");
                    await Task.Delay(5000, cancellationTokenSource.Token); // Wait 5 seconds before retrying
                }
            }
        }
        catch (OperationCanceledException)
        {
            logger.LogInformation("Trading loop cancelled");
        }

        logger.LogInformation("Trading loop stopped");

        // Send shutdown notification
        if (notificationService != null)
        {
            await notificationService.SendSystemShutdownNotificationAsync();
        }

        // Log exception statistics
        var stats = await exceptionHandler.GetExceptionStatisticsAsync();
        if (stats.TotalExceptions > 0)
        {
            logger.LogInformation("Session exception statistics: Total: {Total}, Last 24h: {Last24h}, Last hour: {LastHour}",
                                stats.TotalExceptions, stats.ExceptionsLast24Hours, stats.ExceptionsLastHour);
        }
    }

    static async Task RunSafetyValidation()
    {
        try
        {
            Log.Information("🔍 Starting comprehensive safety validation...");

            var host = CreateHostBuilder(new string[0]).Build();
            var safetyValidator = host.Services.GetRequiredService<SafetyValidator>();
            var alpacaService = host.Services.GetRequiredService<IAlpacaService>();

            // Initialize Alpaca to get account info
            await alpacaService.InitializeAsync();
            var account = await alpacaService.GetAccountAsync();

            // Run safety validation
            var result = await safetyValidator.ValidateSystemSafetyAsync(account.Equity ?? 0);

            // Display results
            Console.WriteLine(result.GenerateReport());

            // Save report to file
            var reportPath = $"logs/safety-validation-{DateTime.Now:yyyyMMdd-HHmmss}.txt";
            await File.WriteAllTextAsync(reportPath, result.GenerateReport());
            Log.Information($"Safety validation report saved to: {reportPath}");

            // Exit with appropriate code
            Environment.Exit(result.IsSafeForTrading() ? 0 : 1);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error during safety validation");
            Environment.Exit(2);
        }
    }
}
