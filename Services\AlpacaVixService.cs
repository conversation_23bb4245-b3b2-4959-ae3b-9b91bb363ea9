using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Enhanced VIX service that uses Alpaca's Algo Trader Plus subscription
/// to get VIX-equivalent data from VIX proxy ETFs via SyntheticVixService
/// </summary>
public interface IAlpacaVixService
{
    Task<decimal> GetCurrentVixAsync();
    Task<decimal> GetVixChangeAsync(TimeSpan period);
    Task<VixAnalysis> GetVixAnalysisAsync();
    Task<bool> TestConnectionAsync();
}

public class AlpacaVixService : IAlpacaVixService
{
    private readonly ILogger<AlpacaVixService> _logger;
    private readonly ISyntheticVixService _syntheticVixService;
    private readonly IPolygonDataService _polygonDataService;
    private readonly IConfiguration _configuration;

    // Cache for VIX data
    private decimal _cachedVix = 0;
    private DateTime _lastVixUpdate = DateTime.MinValue;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(1);

    public AlpacaVixService(
        ILogger<AlpacaVixService> logger,
        ISyntheticVixService syntheticVixService,
        IPolygonDataService polygonDataService,
        IConfiguration configuration)
    {
        _logger = logger;
        _syntheticVixService = syntheticVixService;
        _polygonDataService = polygonDataService;
        _configuration = configuration;

        _logger.LogInformation("AlpacaVixService initialized with SyntheticVixService integration");
    }

    public async Task<decimal> GetCurrentVixAsync()
    {
        try
        {
            // Check cache first
            if (_cachedVix > 0 && DateTime.UtcNow - _lastVixUpdate < _cacheExpiry)
            {
                _logger.LogDebug($"Returning cached SyntheticVIX: {_cachedVix:F2}");
                return _cachedVix;
            }

            _logger.LogDebug("Getting SyntheticVIX using enhanced synthetic model");

            // Primary: Use SyntheticVixService with z-score normalization
            var syntheticVix = await _syntheticVixService.GetCurrentSyntheticVixAsync();
            if (syntheticVix > 0 && syntheticVix < 100) // Sanity check
            {
                _cachedVix = syntheticVix;
                _lastVixUpdate = DateTime.UtcNow;

                _logger.LogInformation($"SyntheticVIX calculated: {syntheticVix:F2}");
                return syntheticVix;
            }

            // Fallback to Polygon if available
            if (_polygonDataService != null)
            {
                try
                {
                    var polygonVix = await _polygonDataService.GetCurrentVixAsync();
                    if (polygonVix > 0)
                    {
                        _logger.LogInformation($"Using Polygon VIX fallback: {polygonVix:F2}");
                        _cachedVix = polygonVix;
                        _lastVixUpdate = DateTime.UtcNow;
                        return polygonVix;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Polygon VIX fallback failed");
                }
            }

            // Final fallback
            _logger.LogWarning("All SyntheticVIX sources failed, using conservative fallback");
            return 20.0m;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current SyntheticVIX");
            return 20.0m;
        }
    }

    public async Task<decimal> GetVixChangeAsync(TimeSpan period)
    {
        try
        {
            // Use SyntheticVixService for change calculation with historical data
            var change = await _syntheticVixService.GetSyntheticVixChangeAsync(period);
            _logger.LogDebug($"SyntheticVIX change over {period}: {change:F2}");
            return change;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating SyntheticVIX change for period {period}");
            return 0m;
        }
    }

    public async Task<VixAnalysis> GetVixAnalysisAsync()
    {
        try
        {
            // Get enhanced analysis from SyntheticVixService
            var syntheticAnalysis = await _syntheticVixService.GetSyntheticVixAnalysisAsync();

            // Convert to legacy VixAnalysis format for backward compatibility
            return new VixAnalysis
            {
                CurrentLevel = syntheticAnalysis.CurrentLevel,
                Interpretation = syntheticAnalysis.Interpretation,
                RiskLevel = syntheticAnalysis.RiskLevel,
                TradingRecommendation = syntheticAnalysis.TradingRecommendation,
                PositionSizeMultiplier = syntheticAnalysis.PositionSizeMultiplier,
                Timestamp = syntheticAnalysis.Timestamp
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing SyntheticVIX analysis");
            return new VixAnalysis
            {
                CurrentLevel = 20.0m,
                Interpretation = "Error - Using Conservative Default",
                RiskLevel = "Medium",
                TradingRecommendation = "Proceed with caution",
                PositionSizeMultiplier = 0.8m,
                Timestamp = DateTime.UtcNow
            };
        }
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logger.LogInformation("Testing Alpaca SyntheticVIX service connection...");

            // Test SyntheticVixService connection
            var syntheticVixTest = await _syntheticVixService.TestConnectionAsync();
            if (syntheticVixTest)
            {
                _logger.LogInformation("Alpaca SyntheticVIX service test successful");
                return true;
            }

            _logger.LogWarning("Alpaca SyntheticVIX service test failed");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Alpaca SyntheticVIX service connection test failed");
            return false;
        }
    }

}

// Legacy VixAnalysis class for backward compatibility
public class VixAnalysis
{
    public decimal CurrentLevel { get; set; }
    public string Interpretation { get; set; } = string.Empty;
    public string RiskLevel { get; set; } = string.Empty;
    public string TradingRecommendation { get; set; } = string.Empty;
    public decimal PositionSizeMultiplier { get; set; }
    public DateTime Timestamp { get; set; }
}
