using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IOptionsPricingService
{
    Task<decimal> CalculateBlackScholesPrice(decimal underlyingPrice, decimal strikePrice, decimal timeToExpiry, decimal riskFreeRate, decimal volatility, bool isCall);
    Task<OptionGreeks> CalculateGreeksAsync(OptionContract contract, decimal underlyingPrice, decimal volatility, decimal riskFreeRate);
    decimal CalculateDelta(decimal underlyingPrice, decimal strikePrice, decimal timeToExpiry, decimal riskFreeRate, decimal volatility, bool isCall);
    decimal CalculateGamma(decimal underlyingPrice, decimal strikePrice, decimal timeToExpiry, decimal riskFreeRate, decimal volatility);
    decimal CalculateTheta(decimal underlyingPrice, decimal strikePrice, decimal timeToExpiry, decimal riskFreeRate, decimal volatility, bool isCall);
    decimal CalculateVega(decimal underlyingPrice, decimal strikePrice, decimal timeToExpiry, decimal riskFreeRate, decimal volatility);
}

public class OptionsPricingService : IOptionsPricingService
{
    private readonly ILogger<OptionsPricingService> _logger;

    public OptionsPricingService(ILogger<OptionsPricingService> logger)
    {
        _logger = logger;
    }

    public async Task<decimal> CalculateBlackScholesPrice(decimal underlyingPrice, decimal strikePrice, decimal timeToExpiry, decimal riskFreeRate, decimal volatility, bool isCall)
    {
        try
        {
            // Handle edge cases
            if (timeToExpiry <= 0)
            {
                // Option has expired - return intrinsic value
                if (isCall)
                    return Math.Max(0, underlyingPrice - strikePrice);
                else
                    return Math.Max(0, strikePrice - underlyingPrice);
            }

            if (volatility <= 0 || underlyingPrice <= 0 || strikePrice <= 0)
                return 0;

            // Convert to double for math calculations
            var S = (double)underlyingPrice;
            var K = (double)strikePrice;
            var T = (double)timeToExpiry;
            var r = (double)riskFreeRate;
            var sigma = (double)volatility;

            // Calculate d1 and d2
            var d1 = (Math.Log(S / K) + (r + 0.5 * sigma * sigma) * T) / (sigma * Math.Sqrt(T));
            var d2 = d1 - sigma * Math.Sqrt(T);

            // Calculate option price
            double price;
            if (isCall)
            {
                price = S * NormalCDF(d1) - K * Math.Exp(-r * T) * NormalCDF(d2);
            }
            else
            {
                price = K * Math.Exp(-r * T) * NormalCDF(-d2) - S * NormalCDF(-d1);
            }

            return Math.Max(0, (decimal)price);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating Black-Scholes price");
            return 0;
        }
    }

    public async Task<OptionGreeks> CalculateGreeksAsync(OptionContract contract, decimal underlyingPrice, decimal volatility, decimal riskFreeRate)
    {
        var timeToExpiry = contract.DaysToExpiration / 365.25m;
        
        return new OptionGreeks
        {
            Delta = CalculateDelta(underlyingPrice, contract.StrikePrice, timeToExpiry, riskFreeRate, volatility, contract.OptionType == OptionType.Call),
            Gamma = CalculateGamma(underlyingPrice, contract.StrikePrice, timeToExpiry, riskFreeRate, volatility),
            Theta = CalculateTheta(underlyingPrice, contract.StrikePrice, timeToExpiry, riskFreeRate, volatility, contract.OptionType == OptionType.Call),
            Vega = CalculateVega(underlyingPrice, contract.StrikePrice, timeToExpiry, riskFreeRate, volatility),
            Timestamp = DateTime.UtcNow
        };
    }

    public decimal CalculateDelta(decimal underlyingPrice, decimal strikePrice, decimal timeToExpiry, decimal riskFreeRate, decimal volatility, bool isCall)
    {
        try
        {
            if (timeToExpiry <= 0)
            {
                // At expiration, delta is 1 for ITM calls, 0 for OTM calls, -1 for ITM puts, 0 for OTM puts
                if (isCall)
                    return underlyingPrice > strikePrice ? 1 : 0;
                else
                    return underlyingPrice < strikePrice ? -1 : 0;
            }

            var S = (double)underlyingPrice;
            var K = (double)strikePrice;
            var T = (double)timeToExpiry;
            var r = (double)riskFreeRate;
            var sigma = (double)volatility;

            var d1 = (Math.Log(S / K) + (r + 0.5 * sigma * sigma) * T) / (sigma * Math.Sqrt(T));

            if (isCall)
                return (decimal)NormalCDF(d1);
            else
                return (decimal)(NormalCDF(d1) - 1);
        }
        catch
        {
            return 0;
        }
    }

    public decimal CalculateGamma(decimal underlyingPrice, decimal strikePrice, decimal timeToExpiry, decimal riskFreeRate, decimal volatility)
    {
        try
        {
            if (timeToExpiry <= 0) return 0;

            var S = (double)underlyingPrice;
            var K = (double)strikePrice;
            var T = (double)timeToExpiry;
            var r = (double)riskFreeRate;
            var sigma = (double)volatility;

            var d1 = (Math.Log(S / K) + (r + 0.5 * sigma * sigma) * T) / (sigma * Math.Sqrt(T));
            var gamma = NormalPDF(d1) / (S * sigma * Math.Sqrt(T));

            return (decimal)gamma;
        }
        catch
        {
            return 0;
        }
    }

    public decimal CalculateTheta(decimal underlyingPrice, decimal strikePrice, decimal timeToExpiry, decimal riskFreeRate, decimal volatility, bool isCall)
    {
        try
        {
            if (timeToExpiry <= 0) return 0;

            var S = (double)underlyingPrice;
            var K = (double)strikePrice;
            var T = (double)timeToExpiry;
            var r = (double)riskFreeRate;
            var sigma = (double)volatility;

            var d1 = (Math.Log(S / K) + (r + 0.5 * sigma * sigma) * T) / (sigma * Math.Sqrt(T));
            var d2 = d1 - sigma * Math.Sqrt(T);

            var term1 = -(S * NormalPDF(d1) * sigma) / (2 * Math.Sqrt(T));
            
            double theta;
            if (isCall)
            {
                var term2 = r * K * Math.Exp(-r * T) * NormalCDF(d2);
                theta = (term1 - term2) / 365.25; // Convert to daily theta
            }
            else
            {
                var term2 = r * K * Math.Exp(-r * T) * NormalCDF(-d2);
                theta = (term1 + term2) / 365.25; // Convert to daily theta
            }

            return (decimal)theta;
        }
        catch
        {
            return 0;
        }
    }

    public decimal CalculateVega(decimal underlyingPrice, decimal strikePrice, decimal timeToExpiry, decimal riskFreeRate, decimal volatility)
    {
        try
        {
            if (timeToExpiry <= 0) return 0;

            var S = (double)underlyingPrice;
            var K = (double)strikePrice;
            var T = (double)timeToExpiry;
            var r = (double)riskFreeRate;
            var sigma = (double)volatility;

            var d1 = (Math.Log(S / K) + (r + 0.5 * sigma * sigma) * T) / (sigma * Math.Sqrt(T));
            var vega = S * NormalPDF(d1) * Math.Sqrt(T) / 100; // Divide by 100 to get vega per 1% change in volatility

            return (decimal)vega;
        }
        catch
        {
            return 0;
        }
    }

    // Standard normal cumulative distribution function
    private static double NormalCDF(double x)
    {
        return 0.5 * (1 + Erf(x / Math.Sqrt(2)));
    }

    // Standard normal probability density function
    private static double NormalPDF(double x)
    {
        return Math.Exp(-0.5 * x * x) / Math.Sqrt(2 * Math.PI);
    }

    // Error function approximation
    private static double Erf(double x)
    {
        // Abramowitz and Stegun approximation
        var a1 = 0.254829592;
        var a2 = -0.284496736;
        var a3 = 1.421413741;
        var a4 = -1.453152027;
        var a5 = 1.061405429;
        var p = 0.3275911;

        var sign = x < 0 ? -1 : 1;
        x = Math.Abs(x);

        var t = 1.0 / (1.0 + p * x);
        var y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.Exp(-x * x);

        return sign * y;
    }
}
