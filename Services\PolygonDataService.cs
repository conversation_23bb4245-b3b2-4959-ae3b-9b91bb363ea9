using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IPolygonDataService
{
    Task<decimal> GetCurrentVixAsync();
    Task<decimal> GetVixChangeAsync(TimeSpan period);
    Task<List<VixDataPoint>> GetVixHistoryAsync(DateTime startDate, DateTime endDate);
    Task<PolygonQuote> GetCurrentQuoteAsync(string symbol);
    Task<PolygonQuote> GetQuoteAsync(string symbol); // Added missing method
    Task<bool> TestConnectionAsync();

    // Options data methods
    Task<List<PolygonOptionsContract>> GetOptionsContractsAsync(string underlyingSymbol, DateTime? expirationDate = null);
    Task<List<PolygonOptionsContract>> GetOptionsContractsForDateAsync(string underlyingSymbol, DateTime expirationDate);
    Task<decimal> GetHistoricalPriceAsync(string symbol, DateTime date);
    Task<bool> IsMarketOpenAsync();
}

public class PolygonDataService : IPolygonDataService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<PolygonDataService> _logger;
    private readonly HttpClient _httpClient;
    private readonly ISecurityService _securityService;
    private string? _apiKey;
    private readonly string _baseUrl = "https://api.polygon.io";

    // Cache for VIX data to avoid excessive API calls
    private decimal _cachedVix = 0;
    private DateTime _lastVixUpdate = DateTime.MinValue;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(1); // Cache for 1 minute

    public PolygonDataService(IConfiguration configuration, ILogger<PolygonDataService> logger, HttpClient httpClient, ISecurityService securityService)
    {
        _configuration = configuration;
        _logger = logger;
        _httpClient = httpClient;
        _securityService = securityService;

        // Configure HttpClient
        _httpClient.BaseAddress = new Uri(_baseUrl);
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "ZeroDateStrat/1.0");
    }

    private async Task<string> GetSecurePolygonApiKeyAsync()
    {
        if (_apiKey != null)
            return _apiKey;

        var configKey = _configuration["Polygon:ApiKey"];
        if (string.IsNullOrEmpty(configKey))
            throw new InvalidOperationException("Polygon API key not configured");

        // Check if the key is encrypted (starts with encrypted prefix)
        if (configKey.StartsWith("ENC:"))
        {
            var encryptedKey = configKey.Substring(4);
            _apiKey = await _securityService.DecryptSensitiveDataAsync(encryptedKey);
        }
        else
        {
            _apiKey = configKey;
        }

        return _apiKey;
    }

    public async Task<decimal> GetCurrentVixAsync()
    {
        try
        {
            // Check cache first
            if (_cachedVix > 0 && DateTime.UtcNow - _lastVixUpdate < _cacheExpiry)
            {
                _logger.LogDebug($"Returning cached VIX: {_cachedVix:F2}");
                return _cachedVix;
            }

            _logger.LogDebug("Fetching current VIX from Polygon.io");

            // Get current VIX quote
            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v2/last/trade/I:VIX?apikey={apiKey}";
            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for VIX request");
                return await GetFallbackVixAsync();
            }

            var content = await response.Content.ReadAsStringAsync();
            var vixResponse = JsonConvert.DeserializeObject<PolygonVixResponse>(content);

            if (vixResponse?.Results?.Price > 0)
            {
                _cachedVix = (decimal)vixResponse.Results.Price;
                _lastVixUpdate = DateTime.UtcNow;
                
                _logger.LogInformation($"Current VIX from Polygon: {_cachedVix:F2}");
                return _cachedVix;
            }

            _logger.LogWarning("Invalid VIX data received from Polygon");
            return await GetFallbackVixAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching VIX from Polygon.io");
            return await GetFallbackVixAsync();
        }
    }

    public async Task<decimal> GetVixChangeAsync(TimeSpan period)
    {
        try
        {
            var currentVix = await GetCurrentVixAsync();
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.Subtract(period);

            var history = await GetVixHistoryAsync(startDate, endDate);
            
            if (history.Any())
            {
                var previousVix = history.First().Value;
                var change = currentVix - previousVix;
                
                _logger.LogDebug($"VIX change over {period}: {change:F2} (from {previousVix:F2} to {currentVix:F2})");
                return change;
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating VIX change over {period}");
            return 0;
        }
    }

    public async Task<List<VixDataPoint>> GetVixHistoryAsync(DateTime startDate, DateTime endDate)
    {
        return await GetVixHistoryAsync(startDate, endDate, "1/day");
    }

    /// <summary>
    /// Gets VIX historical data with configurable time intervals
    /// Supports: 1/minute, 5/minute, 15/minute, 30/minute, 1/hour, 1/day
    /// </summary>
    public async Task<List<VixDataPoint>> GetVixHistoryAsync(DateTime startDate, DateTime endDate, string timespan)
    {
        try
        {
            _logger.LogDebug($"Fetching VIX history from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd} with {timespan} intervals");

            var startDateStr = startDate.ToString("yyyy-MM-dd");
            var endDateStr = endDate.ToString("yyyy-MM-dd");

            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v2/aggs/ticker/I:VIX/range/{timespan}/{startDateStr}/{endDateStr}?adjusted=true&sort=desc&limit=50000&apikey={apiKey}";
            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for VIX history request");
                return new List<VixDataPoint>();
            }

            var content = await response.Content.ReadAsStringAsync();
            var historyResponse = JsonConvert.DeserializeObject<PolygonHistoryResponse>(content);

            if (historyResponse?.Results != null)
            {
                var vixHistory = historyResponse.Results.Select(r => new VixDataPoint
                {
                    Date = DateTimeOffset.FromUnixTimeMilliseconds(r.Timestamp).DateTime,
                    Value = (decimal)r.Close,
                    High = (decimal)r.High,
                    Low = (decimal)r.Low,
                    Volume = r.Volume
                }).OrderBy(v => v.Date).ToList();

                _logger.LogDebug($"Retrieved {vixHistory.Count} VIX data points");
                return vixHistory;
            }

            return new List<VixDataPoint>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching VIX history from Polygon.io");
            return new List<VixDataPoint>();
        }
    }

    public async Task<PolygonQuote> GetCurrentQuoteAsync(string symbol)
    {
        try
        {
            _logger.LogDebug($"Fetching current quote for {symbol}");

            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v2/last/nbbo/{symbol}?apikey={apiKey}";
            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for {symbol} quote");
                return new PolygonQuote { Symbol = symbol };
            }

            var content = await response.Content.ReadAsStringAsync();
            var quoteResponse = JsonConvert.DeserializeObject<PolygonQuoteResponse>(content);

            if (quoteResponse?.Results != null)
            {
                return new PolygonQuote
                {
                    Symbol = symbol,
                    Bid = (decimal)quoteResponse.Results.Bid,
                    Ask = (decimal)quoteResponse.Results.Ask,
                    BidSize = quoteResponse.Results.BidSize,
                    AskSize = quoteResponse.Results.AskSize,
                    Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(quoteResponse.Results.Timestamp).DateTime
                };
            }

            return new PolygonQuote { Symbol = symbol };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching quote for {symbol}");
            return new PolygonQuote { Symbol = symbol };
        }
    }

    public async Task<PolygonQuote> GetQuoteAsync(string symbol)
    {
        // Alias for GetCurrentQuoteAsync to satisfy interface requirements
        return await GetCurrentQuoteAsync(symbol);
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logger.LogInformation("Testing Polygon.io connection...");

            // Try the simplest possible endpoint - market status
            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v1/marketstatus/now?apikey={apiKey}";
            var response = await _httpClient.GetAsync(url);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Polygon.io connection test successful");
                return true;
            }

            // Log more details about the failure
            var content = await response.Content.ReadAsStringAsync();
            _logger.LogWarning($"Polygon.io connection test failed: {response.StatusCode}");
            _logger.LogDebug($"Response content: {content}");

            // If market status fails, try a basic stock quote
            _logger.LogInformation("Testing basic stock quote endpoint...");
            var stockUrl = $"/v2/last/trade/AAPL?apikey={apiKey}";
            var stockResponse = await _httpClient.GetAsync(stockUrl);

            if (stockResponse.IsSuccessStatusCode)
            {
                _logger.LogInformation("Stock quote endpoint accessible - connection is working");
                return true;
            }
            else
            {
                var stockContent = await stockResponse.Content.ReadAsStringAsync();
                _logger.LogWarning($"Stock quote endpoint also failed: {stockResponse.StatusCode}");
                _logger.LogDebug($"Stock response content: {stockContent}");
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Polygon.io connection test failed");
            return false;
        }
    }

    public async Task<List<PolygonOptionsContract>> GetOptionsContractsAsync(string underlyingSymbol, DateTime? expirationDate = null)
    {
        try
        {
            _logger.LogDebug($"Fetching options contracts for {underlyingSymbol}");

            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v3/reference/options/contracts?underlying_ticker={underlyingSymbol}&limit=1000&apikey={apiKey}";

            if (expirationDate.HasValue)
            {
                var expDateStr = expirationDate.Value.ToString("yyyy-MM-dd");
                url += $"&expiration_date={expDateStr}";
            }

            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for options contracts request");
                return new List<PolygonOptionsContract>();
            }

            var content = await response.Content.ReadAsStringAsync();
            var optionsResponse = JsonConvert.DeserializeObject<PolygonOptionsContractsResponse>(content);

            if (optionsResponse?.Results != null)
            {
                _logger.LogInformation($"Retrieved {optionsResponse.Results.Count} options contracts for {underlyingSymbol}");
                return optionsResponse.Results;
            }

            return new List<PolygonOptionsContract>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching options contracts for {underlyingSymbol}");
            return new List<PolygonOptionsContract>();
        }
    }

    public async Task<List<PolygonOptionsContract>> GetOptionsContractsForDateAsync(string underlyingSymbol, DateTime expirationDate)
    {
        return await GetOptionsContractsAsync(underlyingSymbol, expirationDate);
    }

    public async Task<decimal> GetHistoricalPriceAsync(string symbol, DateTime date)
    {
        try
        {
            _logger.LogDebug($"Fetching historical price for {symbol} on {date:yyyy-MM-dd}");

            var apiKey = await GetSecurePolygonApiKeyAsync();
            var dateStr = date.ToString("yyyy-MM-dd");
            var url = $"/v2/aggs/ticker/{symbol}/range/1/day/{dateStr}/{dateStr}?adjusted=true&apikey={apiKey}";

            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for historical price request");
                return 0;
            }

            var content = await response.Content.ReadAsStringAsync();
            var historyResponse = JsonConvert.DeserializeObject<PolygonHistoryResponse>(content);

            if (historyResponse?.Results?.Any() == true)
            {
                var result = historyResponse.Results.First();
                var price = (decimal)result.Close;
                _logger.LogDebug($"Historical price for {symbol} on {date:yyyy-MM-dd}: ${price:F2}");
                return price;
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching historical price for {symbol} on {date:yyyy-MM-dd}");
            return 0;
        }
    }

    public async Task<bool> IsMarketOpenAsync()
    {
        try
        {
            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v1/marketstatus/now?apikey={apiKey}";
            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Could not get market status: {response.StatusCode}");
                return false;
            }

            var content = await response.Content.ReadAsStringAsync();
            var marketStatus = JsonConvert.DeserializeObject<PolygonMarketStatusResponse>(content);

            // Check if NYSE is open (primary indicator for US equity markets)
            return marketStatus?.Exchanges?.Nyse == "open";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking market status");
            return false;
        }
    }

    private async Task<decimal> GetFallbackVixAsync()
    {
        try
        {
            // If we have a cached value that's not too old, use it
            if (_cachedVix > 0 && DateTime.UtcNow - _lastVixUpdate < TimeSpan.FromHours(1))
            {
                _logger.LogDebug($"Using cached VIX as fallback: {_cachedVix:F2}");
                return _cachedVix;
            }

            // Otherwise, use a reasonable default based on current market conditions
            var fallbackVix = 20.0m; // Reasonable default
            _logger.LogWarning($"Using fallback VIX value: {fallbackVix:F2}");
            return fallbackVix;
        }
        catch
        {
            return 20.0m; // Safe default
        }
    }
}

// Polygon.io response models
public class PolygonVixResponse
{
    [JsonProperty("results")]
    public PolygonVixResult? Results { get; set; }
}

public class PolygonVixResult
{
    [JsonProperty("p")]
    public double Price { get; set; }
    
    [JsonProperty("t")]
    public long Timestamp { get; set; }
}

public class PolygonHistoryResponse
{
    [JsonProperty("results")]
    public List<PolygonHistoryResult>? Results { get; set; }
}

public class PolygonHistoryResult
{
    [JsonProperty("c")]
    public double Close { get; set; }
    
    [JsonProperty("h")]
    public double High { get; set; }
    
    [JsonProperty("l")]
    public double Low { get; set; }
    
    [JsonProperty("v")]
    public long Volume { get; set; }
    
    [JsonProperty("t")]
    public long Timestamp { get; set; }
}

public class PolygonQuoteResponse
{
    [JsonProperty("results")]
    public PolygonQuoteResult? Results { get; set; }
}

public class PolygonQuoteResult
{
    [JsonProperty("bid")]
    public double Bid { get; set; }
    
    [JsonProperty("ask")]
    public double Ask { get; set; }
    
    [JsonProperty("bidSize")]
    public int BidSize { get; set; }
    
    [JsonProperty("askSize")]
    public int AskSize { get; set; }
    
    [JsonProperty("t")]
    public long Timestamp { get; set; }
}

public class PolygonQuote
{
    public string Symbol { get; set; } = string.Empty;
    public decimal Bid { get; set; }
    public decimal Ask { get; set; }
    public int BidSize { get; set; }
    public int AskSize { get; set; }
    public DateTime Timestamp { get; set; }
    public decimal MidPrice => (Bid + Ask) / 2;
    public decimal Spread => Ask - Bid;
    public decimal SpreadPercentage => MidPrice > 0 ? (Spread / MidPrice) * 100 : 0;
}

// Options-related models
public class PolygonOptionsContractsResponse
{
    [JsonProperty("results")]
    public List<PolygonOptionsContract> Results { get; set; } = new();

    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;

    [JsonProperty("count")]
    public int Count { get; set; }
}

public class PolygonOptionsContract
{
    [JsonProperty("ticker")]
    public string Ticker { get; set; } = string.Empty;

    [JsonProperty("underlying_ticker")]
    public string UnderlyingTicker { get; set; } = string.Empty;

    [JsonProperty("expiration_date")]
    public string ExpirationDate { get; set; } = string.Empty;

    [JsonProperty("strike_price")]
    public decimal StrikePrice { get; set; }

    [JsonProperty("contract_type")]
    public string ContractType { get; set; } = string.Empty;

    [JsonProperty("shares_per_contract")]
    public int SharesPerContract { get; set; } = 100;

    [JsonProperty("exercise_style")]
    public string ExerciseStyle { get; set; } = string.Empty;

    [JsonProperty("primary_exchange")]
    public string PrimaryExchange { get; set; } = string.Empty;

    // Helper properties
    public DateTime ExpirationDateTime => DateTime.TryParse(ExpirationDate, out var date) ? date : DateTime.MinValue;
    public bool IsCall => ContractType?.ToLower() == "call";
    public bool IsPut => ContractType?.ToLower() == "put";
}

public class PolygonMarketStatusResponse
{
    [JsonProperty("exchanges")]
    public PolygonExchangeStatus? Exchanges { get; set; }

    [JsonProperty("afterHours")]
    public bool AfterHours { get; set; }

    [JsonProperty("earlyHours")]
    public bool EarlyHours { get; set; }
}

public class PolygonExchangeStatus
{
    [JsonProperty("nyse")]
    public string Nyse { get; set; } = string.Empty;

    [JsonProperty("nasdaq")]
    public string Nasdaq { get; set; } = string.Empty;

    [JsonProperty("otc")]
    public string Otc { get; set; } = string.Empty;
}
