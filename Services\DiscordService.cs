using Discord;
using Discord.Net;
using Discord.WebSocket;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using Alpaca.Markets;

namespace ZeroDateStrat.Services;

public interface IDiscordService
{
    Task StartAsync(CancellationToken cancellationToken = default);
    Task StopAsync(CancellationToken cancellationToken = default);
    Task SendAlertAsync(RiskAlert alert);
    Task SendMessageAsync(string message);
    Task<bool> IsConnectedAsync();
    event Func<string, string, Task> OnCommandReceived;
    event Func<string, Task> OnMessageReceived;

    // Enhanced notification methods
    Task SendMorningPortfolioSummaryAsync(PortfolioSnapshot portfolio, IAccount account);
    Task SendTradeNotificationAsync(TradingSignal signal, IOrder? order, bool success);
    Task SendEndOfDayReportAsync(DailyTradingReport report);
    Task SendPositionUpdateAsync(Models.ManagedPosition position, string updateType);
    Task SendMarketStatusUpdateAsync(string marketStatus, decimal vix, string marketRegime);

    // Error notification methods
    Task SendTradingErrorAsync(Exception exception, string context, string severity = "Medium");

    // Guidance request methods
    Task SendGuidanceRequestAsync(string message);
    Task<bool> IsMessageFromChatGptBot(SocketMessage message);
    event Func<SocketMessage, Task> OnChatGptBotMessageReceived;
}

public class DiscordService : IDiscordService, IHostedService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<DiscordService> _logger;
    private readonly IChatGPTBotHandler? _chatGPTBotHandler;
    private DiscordSocketClient? _client;
    private bool _isRunning = false;
    private ulong _channelId;
    private bool _useEmbeds;

    public event Func<string, string, Task>? OnCommandReceived;
    public event Func<string, Task>? OnMessageReceived;
    public event Func<SocketMessage, Task>? OnChatGptBotMessageReceived;

    public DiscordService(IConfiguration configuration, ILogger<DiscordService> logger, IChatGPTBotHandler? chatGPTBotHandler = null)
    {
        _configuration = configuration;
        _logger = logger;
        _chatGPTBotHandler = chatGPTBotHandler;
    }

    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var discordConfig = _configuration.GetSection("Monitoring:NotificationChannels:Discord");
            
            if (!discordConfig.GetValue<bool>("Enabled", false))
            {
                _logger.LogInformation("Discord service is disabled");
                return;
            }

            if (!discordConfig.GetValue<bool>("EnableSlashCommands", false))
            {
                _logger.LogInformation("Discord slash commands are disabled");
                return;
            }

            var botToken = Environment.GetEnvironmentVariable("DISCORD_BOT_TOKEN")
                          ?? discordConfig.GetValue<string>("BotToken");
            _channelId = discordConfig.GetValue<ulong>("ChannelId", 0);
            _useEmbeds = discordConfig.GetValue<bool>("UseEmbeds", true);

            if (string.IsNullOrEmpty(botToken) || _channelId == 0)
            {
                _logger.LogWarning("Discord bot token or channel ID not configured");
                return;
            }

            _client = new DiscordSocketClient();
            
            _client.Log += LogAsync;
            _client.Ready += ReadyAsync;
            _client.MessageReceived += MessageReceivedAsync;
            _client.SlashCommandExecuted += SlashCommandHandler;

            await _client.LoginAsync(TokenType.Bot, botToken);
            await _client.StartAsync();

            _isRunning = true;
            _logger.LogInformation("Discord service started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start Discord service");
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_client != null && _isRunning)
        {
            await _client.LogoutAsync();
            await _client.StopAsync();
            _client.Dispose();
            _isRunning = false;
            _logger.LogInformation("Discord service stopped");
        }
    }

    public async Task SendAlertAsync(RiskAlert alert)
    {
        if (!_isRunning || _client == null)
        {
            _logger.LogWarning("Discord service is not running");
            return;
        }

        try
        {
            var channel = _client.GetChannel(_channelId) as IMessageChannel;
            if (channel == null)
            {
                _logger.LogWarning($"Discord channel {_channelId} not found");
                return;
            }

            if (_useEmbeds)
            {
                var embed = BuildAlertEmbed(alert);
                await channel.SendMessageAsync(embed: embed);
            }
            else
            {
                var message = BuildAlertMessage(alert);
                await channel.SendMessageAsync(message);
            }

            _logger.LogInformation($"Discord alert sent for {alert.Type}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to send Discord alert for {alert.Type}");
        }
    }

    public async Task SendMessageAsync(string message)
    {
        if (!_isRunning || _client == null)
        {
            _logger.LogWarning("Discord service is not running");
            return;
        }

        try
        {
            var channel = _client.GetChannel(_channelId) as IMessageChannel;
            if (channel != null)
            {
                await channel.SendMessageAsync(message);
                _logger.LogDebug("Discord message sent successfully");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send Discord message");
        }
    }

    public Task<bool> IsConnectedAsync()
    {
        return Task.FromResult(_isRunning && _client?.ConnectionState == ConnectionState.Connected);
    }

    public async Task SendMorningPortfolioSummaryAsync(PortfolioSnapshot portfolio, IAccount account)
    {
        if (!_isRunning || _client == null)
        {
            _logger.LogWarning("Discord service is not running");
            return;
        }

        try
        {
            var channel = _client.GetChannel(_channelId) as IMessageChannel;
            if (channel == null)
            {
                _logger.LogWarning($"Discord channel {_channelId} not found");
                return;
            }

            if (_useEmbeds)
            {
                var embed = BuildMorningPortfolioEmbed(portfolio, account);
                await channel.SendMessageAsync(embed: embed);
            }
            else
            {
                var message = BuildMorningPortfolioMessage(portfolio, account);
                await channel.SendMessageAsync(message);
            }

            _logger.LogInformation("Morning portfolio summary sent to Discord");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send morning portfolio summary to Discord");
        }
    }

    public async Task SendTradeNotificationAsync(TradingSignal signal, IOrder? order, bool success)
    {
        if (!_isRunning || _client == null)
        {
            _logger.LogWarning("Discord service is not running");
            return;
        }

        try
        {
            var channel = _client.GetChannel(_channelId) as IMessageChannel;
            if (channel == null)
            {
                _logger.LogWarning($"Discord channel {_channelId} not found");
                return;
            }

            if (_useEmbeds)
            {
                var embed = BuildTradeNotificationEmbed(signal, order, success);
                await channel.SendMessageAsync(embed: embed);
            }
            else
            {
                var message = BuildTradeNotificationMessage(signal, order, success);
                await channel.SendMessageAsync(message);
            }

            _logger.LogInformation($"Trade notification sent to Discord: {signal.Strategy} - {(success ? "Success" : "Failed")}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send trade notification to Discord");
        }
    }

    public async Task SendEndOfDayReportAsync(DailyTradingReport report)
    {
        if (!_isRunning || _client == null)
        {
            _logger.LogWarning("Discord service is not running");
            return;
        }

        try
        {
            var channel = _client.GetChannel(_channelId) as IMessageChannel;
            if (channel == null)
            {
                _logger.LogWarning($"Discord channel {_channelId} not found");
                return;
            }

            if (_useEmbeds)
            {
                var embed = BuildEndOfDayReportEmbed(report);
                await channel.SendMessageAsync(embed: embed);
            }
            else
            {
                var message = BuildEndOfDayReportMessage(report);
                await channel.SendMessageAsync(message);
            }

            _logger.LogInformation("End of day report sent to Discord");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send end of day report to Discord");
        }
    }

    public async Task SendPositionUpdateAsync(Models.ManagedPosition position, string updateType)
    {
        if (!_isRunning || _client == null)
        {
            _logger.LogWarning("Discord service is not running");
            return;
        }

        try
        {
            var channel = _client.GetChannel(_channelId) as IMessageChannel;
            if (channel == null)
            {
                _logger.LogWarning($"Discord channel {_channelId} not found");
                return;
            }

            if (_useEmbeds)
            {
                var embed = BuildPositionUpdateEmbed(position, updateType);
                await channel.SendMessageAsync(embed: embed);
            }
            else
            {
                var message = BuildPositionUpdateMessage(position, updateType);
                await channel.SendMessageAsync(message);
            }

            _logger.LogInformation($"Position update sent to Discord: {position.UnderlyingSymbol} - {updateType}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send position update to Discord");
        }
    }

    public async Task SendMarketStatusUpdateAsync(string marketStatus, decimal vix, string marketRegime)
    {
        if (!_isRunning || _client == null)
        {
            _logger.LogWarning("Discord service is not running");
            return;
        }

        try
        {
            var channel = _client.GetChannel(_channelId) as IMessageChannel;
            if (channel == null)
            {
                _logger.LogWarning($"Discord channel {_channelId} not found");
                return;
            }

            if (_useEmbeds)
            {
                var embed = BuildMarketStatusEmbed(marketStatus, vix, marketRegime);
                await channel.SendMessageAsync(embed: embed);
            }
            else
            {
                var message = BuildMarketStatusMessage(marketStatus, vix, marketRegime);
                await channel.SendMessageAsync(message);
            }

            _logger.LogInformation($"Market status update sent to Discord: {marketStatus}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send market status update to Discord");
        }
    }

    public async Task SendTradingErrorAsync(Exception exception, string context, string severity = "Medium")
    {
        if (!_isRunning || _client == null)
        {
            _logger.LogWarning("Discord service is not running");
            return;
        }

        try
        {
            var channel = _client.GetChannel(_channelId) as IMessageChannel;
            if (channel == null)
            {
                _logger.LogWarning($"Discord channel {_channelId} not found");
                return;
            }

            if (_useEmbeds)
            {
                var embed = BuildTradingErrorEmbed(exception, context, severity);
                await channel.SendMessageAsync(embed: embed);
            }
            else
            {
                var message = BuildTradingErrorMessage(exception, context, severity);
                await channel.SendMessageAsync(message);
            }

            _logger.LogInformation($"Trading error notification sent to Discord: {exception.GetType().Name} in {context}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send trading error notification to Discord");
        }
    }

    public async Task SendGuidanceRequestAsync(string message)
    {
        if (!_isRunning || _client == null)
        {
            _logger.LogWarning("Discord service is not running");
            return;
        }

        try
        {
            var channel = _client.GetChannel(_channelId) as IMessageChannel;
            if (channel != null)
            {
                await channel.SendMessageAsync(message);
                _logger.LogInformation("Guidance request sent to Discord: {Message}", message.Substring(0, Math.Min(50, message.Length)));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send guidance request to Discord");
        }
    }

    public Task<bool> IsMessageFromChatGptBot(SocketMessage message)
    {
        // Check if the message is from ChatGptBot
        // This can be enhanced with specific user ID checking if known
        var isChatGptBot = message.Author.Username?.ToLower().Contains("chatgpt") == true ||
                          message.Author.Username?.ToLower().Contains("gpt") == true ||
                          message.Author.GlobalName?.ToLower().Contains("chatgpt") == true;

        return Task.FromResult(isChatGptBot);
    }

    private Task LogAsync(LogMessage log)
    {
        var logLevel = log.Severity switch
        {
            LogSeverity.Critical => LogLevel.Critical,
            LogSeverity.Error => LogLevel.Error,
            LogSeverity.Warning => LogLevel.Warning,
            LogSeverity.Info => LogLevel.Information,
            LogSeverity.Verbose => LogLevel.Debug,
            LogSeverity.Debug => LogLevel.Trace,
            _ => LogLevel.Information
        };

        _logger.Log(logLevel, log.Exception, "[Discord] {Message}", log.Message);
        return Task.CompletedTask;
    }

    private async Task ReadyAsync()
    {
        _logger.LogInformation($"Discord bot is connected as {_client?.CurrentUser?.Username}");
        
        // Register slash commands
        await RegisterSlashCommandsAsync();
    }

    private async Task RegisterSlashCommandsAsync()
    {
        try
        {
            if (_client?.CurrentUser?.Id == null) return;

            var statusCommand = new SlashCommandBuilder()
                .WithName("status")
                .WithDescription("Get trading system status");

            var portfolioCommand = new SlashCommandBuilder()
                .WithName("portfolio")
                .WithDescription("Get current portfolio information");

            var alertsCommand = new SlashCommandBuilder()
                .WithName("alerts")
                .WithDescription("Get recent alerts");

            var stopCommand = new SlashCommandBuilder()
                .WithName("stop")
                .WithDescription("Emergency stop trading");

            await _client.CreateGlobalApplicationCommandAsync(statusCommand.Build());
            await _client.CreateGlobalApplicationCommandAsync(portfolioCommand.Build());
            await _client.CreateGlobalApplicationCommandAsync(alertsCommand.Build());
            await _client.CreateGlobalApplicationCommandAsync(stopCommand.Build());

            _logger.LogInformation("Discord slash commands registered");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register Discord slash commands");
        }
    }

    private async Task MessageReceivedAsync(SocketMessage message)
    {
        if (message.Channel.Id != _channelId)
            return;

        try
        {
            // Check if this is a message from ChatGptBot
            var isChatGptBot = await IsMessageFromChatGptBot(message);
            if (isChatGptBot)
            {
                OnChatGptBotMessageReceived?.Invoke(message);
                return;
            }

            // Skip other bot messages for regular processing
            if (message.Author.IsBot)
                return;

            // Check if this message should trigger ChatGPT response
            if (_chatGPTBotHandler != null && _chatGPTBotHandler.ShouldTriggerChatGPT(message))
            {
                var handled = await _chatGPTBotHandler.HandleMessageAsync(message);
                if (handled)
                {
                    return; // Message was handled by ChatGPT bot
                }
            }

            var content = message.Content.Trim();
            if (content.StartsWith("!"))
            {
                var parts = content[1..].Split(' ', 2);
                var command = parts[0].ToLower();
                var args = parts.Length > 1 ? parts[1] : "";

                OnCommandReceived?.Invoke(command, args);
            }
            else
            {
                OnMessageReceived?.Invoke(content);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Discord message");
        }
    }

    private async Task SlashCommandHandler(SocketSlashCommand command)
    {
        try
        {
            var commandName = command.Data.Name;
            _logger.LogInformation($"Received Discord slash command: {commandName}");

            OnCommandReceived?.Invoke(commandName, "");

            // Acknowledge the command
            await command.RespondAsync($"Processing {commandName} command...", ephemeral: true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling Discord slash command");
            await command.RespondAsync("An error occurred processing your command.", ephemeral: true);
        }
    }

    private Embed BuildAlertEmbed(RiskAlert alert)
    {
        var color = alert.Severity switch
        {
            RiskLevel.Low => Color.Green,
            RiskLevel.Medium => Color.Orange,
            RiskLevel.High => Color.Red,
            RiskLevel.Critical => Color.DarkRed,
            _ => Color.Gold
        };

        return new EmbedBuilder()
            .WithTitle($"🚨 {alert.Type} Alert")
            .WithDescription(alert.Message)
            .WithColor(color)
            .WithTimestamp(alert.Timestamp)
            .AddField("Severity", alert.Severity.ToString(), true)
            .AddField("Current Value", alert.Value.ToString("F2"), true)
            .AddField("Threshold", alert.Threshold.ToString("F2"), true)
            .AddField("Alert ID", alert.Id, true)
            .WithFooter("Zero DTE Trading System")
            .Build();
    }

    private string BuildAlertMessage(RiskAlert alert)
    {
        var emoji = alert.Severity switch
        {
            RiskLevel.Low => "🟢",
            RiskLevel.Medium => "🟡",
            RiskLevel.High => "🔴",
            RiskLevel.Critical => "🚨",
            _ => "⚠️"
        };

        return $"{emoji} **{alert.Type} Alert - {alert.Severity}**\n" +
               $"📝 {alert.Message}\n" +
               $"📊 Current Value: {alert.Value:F2}\n" +
               $"🎯 Threshold: {alert.Threshold:F2}\n" +
               $"🕒 Time: {alert.Timestamp:yyyy-MM-dd HH:mm:ss} UTC\n" +
               $"🆔 Alert ID: {alert.Id}";
    }

    private Embed BuildMorningPortfolioEmbed(PortfolioSnapshot portfolio, IAccount account)
    {
        var embed = new EmbedBuilder()
            .WithTitle("🌅 Morning Portfolio Summary")
            .WithDescription($"Portfolio status as of {DateTime.Now:yyyy-MM-dd HH:mm:ss ET}")
            .WithColor(portfolio.DayPnL >= 0 ? Color.Green : Color.Red)
            .WithTimestamp(DateTimeOffset.Now);

        // Account Information
        embed.AddField("💰 Account Value", $"${account.Equity:N2}", true);
        embed.AddField("📈 Day P&L", $"${portfolio.DayPnL:+#,##0.00;-#,##0.00;$0.00}", true);
        embed.AddField("💵 Buying Power", $"${portfolio.BuyingPower:N2}", true);

        // Position Information
        embed.AddField("📊 Active Positions", portfolio.ActivePositions.ToString(), true);
        embed.AddField("💼 Total Position P&L", $"${portfolio.TotalPnL:+#,##0.00;-#,##0.00;$0.00}", true);
        embed.AddField("🎯 Available for Trading", $"${account.BuyingPower:N2}", true);

        // Top Positions
        if (portfolio.TopPositions.Any())
        {
            var positionsText = string.Join("\n", portfolio.TopPositions.Take(3).Select(p =>
                $"• {p.Symbol} ({p.Strategy}): ${p.PnL:+#,##0.00;-#,##0.00;$0.00}"));
            embed.AddField("🔝 Top Positions", positionsText, false);
        }

        // Greeks Summary
        if (portfolio.GreeksBySymbol.Any())
        {
            var greeksText = string.Join("\n", portfolio.GreeksBySymbol.Take(3).Select(g =>
                $"• {g.Key}: {g.Value:F2}"));
            embed.AddField("🏛️ Portfolio Greeks", greeksText, false);
        }

        embed.WithFooter("Zero DTE Trading System", "https://cdn.discordapp.com/embed/avatars/0.png");

        return embed.Build();
    }

    private string BuildMorningPortfolioMessage(PortfolioSnapshot portfolio, IAccount account)
    {
        var message = "🌅 **Morning Portfolio Summary**\n\n";
        message += $"**Account Value:** ${account.Equity:N2}\n";
        message += $"**Day P&L:** ${portfolio.DayPnL:+#,##0.00;-#,##0.00;$0.00}\n";
        message += $"**Buying Power:** ${portfolio.BuyingPower:N2}\n";
        message += $"**Active Positions:** {portfolio.ActivePositions}\n";
        message += $"**Total Position P&L:** ${portfolio.TotalPnL:+#,##0.00;-#,##0.00;$0.00}\n\n";

        if (portfolio.TopPositions.Any())
        {
            message += "**Top Positions:**\n";
            foreach (var position in portfolio.TopPositions.Take(3))
            {
                message += $"• {position.Symbol} ({position.Strategy}): ${position.PnL:+#,##0.00;-#,##0.00;$0.00}\n";
            }
        }

        message += $"\n*Updated: {DateTime.Now:yyyy-MM-dd HH:mm:ss ET}*";
        return message;
    }

    private Embed BuildTradeNotificationEmbed(TradingSignal signal, IOrder? order, bool success)
    {
        var color = success ? Color.Green : Color.Red;
        var statusEmoji = success ? "✅" : "❌";
        var title = $"{statusEmoji} Trade {(success ? "Executed" : "Failed")}";

        var embed = new EmbedBuilder()
            .WithTitle(title)
            .WithDescription($"{signal.Strategy} strategy on {signal.UnderlyingSymbol}")
            .WithColor(color)
            .WithTimestamp(DateTimeOffset.Now);

        embed.AddField("📊 Strategy", signal.Strategy, true);
        embed.AddField("🎯 Symbol", signal.UnderlyingSymbol, true);
        embed.AddField("💰 Max Risk", $"${Math.Abs(signal.MaxLoss):N2}", true);

        if (success && order != null)
        {
            embed.AddField("🆔 Order ID", order.OrderId.ToString(), true);
            embed.AddField("📈 Order Status", order.OrderStatus.ToString(), true);
            embed.AddField("⏰ Order Time", DateTime.Now.ToString("HH:mm:ss"), true);
        }

        // Add legs information
        if (signal.Legs.Any())
        {
            var legsText = string.Join("\n", signal.Legs.Select(leg =>
                $"• {leg.Side} {leg.Quantity} {leg.Symbol} @ ${leg.Price:F2}"));
            embed.AddField("🦵 Trade Legs", legsText, false);
        }

        embed.WithFooter("Zero DTE Trading System");
        return embed.Build();
    }

    private string BuildTradeNotificationMessage(TradingSignal signal, IOrder? order, bool success)
    {
        var statusEmoji = success ? "✅" : "❌";
        var message = $"{statusEmoji} **Trade {(success ? "Executed" : "Failed")}**\n\n";

        message += $"**Strategy:** {signal.Strategy}\n";
        message += $"**Symbol:** {signal.UnderlyingSymbol}\n";
        message += $"**Max Risk:** ${Math.Abs(signal.MaxLoss):N2}\n";

        if (success && order != null)
        {
            message += $"**Order ID:** {order.OrderId}\n";
            message += $"**Status:** {order.OrderStatus}\n";
            message += $"**Order Time:** {DateTime.Now.ToString("HH:mm:ss")}\n";
        }

        if (signal.Legs.Any())
        {
            message += "\n**Trade Legs:**\n";
            foreach (var leg in signal.Legs)
            {
                message += $"• {leg.Side} {leg.Quantity} {leg.Symbol} @ ${leg.Price:F2}\n";
            }
        }

        message += $"\n*Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss ET}*";
        return message;
    }

    private Embed BuildEndOfDayReportEmbed(DailyTradingReport report)
    {
        var color = report.DayPnL >= 0 ? Color.Green : Color.Red;
        var profitEmoji = report.DayPnL >= 0 ? "📈" : "📉";

        var embed = new EmbedBuilder()
            .WithTitle($"🌙 End of Day Trading Report - {report.Date:yyyy-MM-dd}")
            .WithDescription($"{profitEmoji} Daily Performance Summary")
            .WithColor(color)
            .WithTimestamp(DateTimeOffset.Now);

        // Performance Metrics
        embed.AddField("💰 Day P&L", $"${report.DayPnL:+#,##0.00;-#,##0.00;$0.00}", true);
        embed.AddField("📊 Total Trades", report.TotalTrades.ToString(), true);
        embed.AddField("🎯 Win Rate", $"{report.WinRate:P1}", true);

        embed.AddField("💵 Account Value", $"${report.EndingAccountValue:N2}", true);
        embed.AddField("📈 Account Change", $"${report.AccountValueChange:+#,##0.00;-#,##0.00;$0.00}", true);
        embed.AddField("🔄 Positions Closed", report.PositionsClosed.ToString(), true);

        // Strategy Performance
        if (report.StrategyPerformance.Any())
        {
            var strategyText = string.Join("\n", report.StrategyPerformance.Take(3).Select(s =>
                $"• {s.Key}: ${s.Value:+#,##0.00;-#,##0.00;$0.00}"));
            embed.AddField("📋 Strategy Performance", strategyText, false);
        }

        // Risk Metrics
        embed.AddField("⚠️ Max Drawdown", $"${report.MaxDrawdown:N2}", true);
        embed.AddField("🎲 Risk Taken", $"${report.TotalRiskTaken:N2}", true);
        embed.AddField("🛡️ Risk Utilization", $"{report.RiskUtilization:P1}", true);

        embed.WithFooter("Zero DTE Trading System - Market Closed");
        return embed.Build();
    }

    private string BuildEndOfDayReportMessage(DailyTradingReport report)
    {
        var profitEmoji = report.DayPnL >= 0 ? "📈" : "📉";
        var message = $"🌙 **End of Day Trading Report - {report.Date:yyyy-MM-dd}**\n\n";

        message += $"{profitEmoji} **Day P&L:** ${report.DayPnL:+#,##0.00;-#,##0.00;$0.00}\n";
        message += $"**Total Trades:** {report.TotalTrades}\n";
        message += $"**Win Rate:** {report.WinRate:P1}\n";
        message += $"**Account Value:** ${report.EndingAccountValue:N2}\n";
        message += $"**Account Change:** ${report.AccountValueChange:+#,##0.00;-#,##0.00;$0.00}\n";
        message += $"**Positions Closed:** {report.PositionsClosed}\n\n";

        if (report.StrategyPerformance.Any())
        {
            message += "**Strategy Performance:**\n";
            foreach (var strategy in report.StrategyPerformance.Take(3))
            {
                message += $"• {strategy.Key}: ${strategy.Value:+#,##0.00;-#,##0.00;$0.00}\n";
            }
            message += "\n";
        }

        message += $"**Max Drawdown:** ${report.MaxDrawdown:N2}\n";
        message += $"**Risk Taken:** ${report.TotalRiskTaken:N2}\n";
        message += $"**Risk Utilization:** {report.RiskUtilization:P1}\n";

        message += $"\n*Report generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss ET}*";
        return message;
    }

    private Embed BuildPositionUpdateEmbed(Models.ManagedPosition position, string updateType)
    {
        var color = updateType.ToLower() switch
        {
            "opened" => Color.Blue,
            "closed" => position.UnrealizedPnL >= 0 ? Color.Green : Color.Red,
            "adjusted" => Color.Orange,
            "profit_target" => Color.Green,
            "stop_loss" => Color.Red,
            _ => Color.Gold
        };

        var emoji = updateType.ToLower() switch
        {
            "opened" => "🆕",
            "closed" => position.UnrealizedPnL >= 0 ? "✅" : "❌",
            "adjusted" => "🔧",
            "profit_target" => "🎯",
            "stop_loss" => "🛑",
            _ => "📊"
        };

        var embed = new EmbedBuilder()
            .WithTitle($"{emoji} Position {updateType}")
            .WithDescription($"{position.Strategy} on {position.UnderlyingSymbol}")
            .WithColor(color)
            .WithTimestamp(DateTimeOffset.Now);

        embed.AddField("🎯 Symbol", position.UnderlyingSymbol, true);
        embed.AddField("📋 Strategy", position.Strategy, true);
        embed.AddField("💰 Current Value", $"${position.CurrentValue:N2}", true);

        embed.AddField("📈 P&L", $"${position.UnrealizedPnL:+#,##0.00;-#,##0.00;$0.00}", true);
        embed.AddField("💵 Open Credit", $"${position.OpenCredit:N2}", true);
        embed.AddField("⏰ Days to Exp", $"{(position.ExpirationDate - DateTime.Now).TotalDays:F1}", true);

        if (updateType.ToLower() == "closed")
        {
            embed.AddField("🏁 Exit Reason", position.ExitReason ?? "Manual", false);
        }

        embed.WithFooter("Zero DTE Trading System");
        return embed.Build();
    }

    private string BuildPositionUpdateMessage(Models.ManagedPosition position, string updateType)
    {
        var emoji = updateType.ToLower() switch
        {
            "opened" => "🆕",
            "closed" => position.UnrealizedPnL >= 0 ? "✅" : "❌",
            "adjusted" => "🔧",
            "profit_target" => "🎯",
            "stop_loss" => "🛑",
            _ => "📊"
        };

        var message = $"{emoji} **Position {updateType}**\n\n";
        message += $"**Symbol:** {position.UnderlyingSymbol}\n";
        message += $"**Strategy:** {position.Strategy}\n";
        message += $"**Current Value:** ${position.CurrentValue:N2}\n";
        message += $"**P&L:** ${position.UnrealizedPnL:+#,##0.00;-#,##0.00;$0.00}\n";
        message += $"**Open Credit:** ${position.OpenCredit:N2}\n";
        message += $"**Days to Expiration:** {(position.ExpirationDate - DateTime.Now).TotalDays:F1}\n";

        if (updateType.ToLower() == "closed")
        {
            message += $"**Exit Reason:** {position.ExitReason ?? "Manual"}\n";
        }

        message += $"\n*Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss ET}*";
        return message;
    }

    private Embed BuildMarketStatusEmbed(string marketStatus, decimal vix, string marketRegime)
    {
        var color = marketStatus.ToLower() switch
        {
            "open" => Color.Green,
            "closed" => Color.Red,
            "pre-market" => Color.Orange,
            "after-hours" => Color.Purple,
            _ => Color.Gold
        };

        var statusEmoji = marketStatus.ToLower() switch
        {
            "open" => "🟢",
            "closed" => "🔴",
            "pre-market" => "🟡",
            "after-hours" => "🟣",
            _ => "⚪"
        };

        var vixEmoji = vix switch
        {
            < 20 => "😴",
            < 30 => "😐",
            < 40 => "😰",
            _ => "🚨"
        };

        var embed = new EmbedBuilder()
            .WithTitle($"{statusEmoji} Market Status Update")
            .WithDescription($"Current market conditions and trading environment")
            .WithColor(color)
            .WithTimestamp(DateTimeOffset.Now);

        embed.AddField("📊 Market Status", marketStatus, true);
        embed.AddField($"{vixEmoji} VIX Level", $"{vix:F2}", true);
        embed.AddField("🌡️ Market Regime", marketRegime, true);

        var vixDescription = vix switch
        {
            < 20 => "Low volatility - Calm market conditions",
            < 30 => "Normal volatility - Standard trading environment",
            < 40 => "Elevated volatility - Increased market stress",
            _ => "High volatility - Extreme market conditions"
        };

        embed.AddField("📈 VIX Interpretation", vixDescription, false);

        embed.WithFooter("Zero DTE Trading System");
        return embed.Build();
    }

    private string BuildMarketStatusMessage(string marketStatus, decimal vix, string marketRegime)
    {
        var statusEmoji = marketStatus.ToLower() switch
        {
            "open" => "🟢",
            "closed" => "🔴",
            "pre-market" => "🟡",
            "after-hours" => "🟣",
            _ => "⚪"
        };

        var vixEmoji = vix switch
        {
            < 20 => "😴",
            < 30 => "😐",
            < 40 => "😰",
            _ => "🚨"
        };

        var message = $"{statusEmoji} **Market Status Update**\n\n";
        message += $"**Market Status:** {marketStatus}\n";
        message += $"**VIX Level:** {vixEmoji} {vix:F2}\n";
        message += $"**Market Regime:** {marketRegime}\n\n";

        var vixDescription = vix switch
        {
            < 20 => "Low volatility - Calm market conditions",
            < 30 => "Normal volatility - Standard trading environment",
            < 40 => "Elevated volatility - Increased market stress",
            _ => "High volatility - Extreme market conditions"
        };

        message += $"**VIX Interpretation:** {vixDescription}\n";
        message += $"\n*Updated: {DateTime.Now:yyyy-MM-dd HH:mm:ss ET}*";
        return message;
    }

    private Embed BuildTradingErrorEmbed(Exception exception, string context, string severity)
    {
        var color = severity.ToLower() switch
        {
            "low" => Color.Orange,
            "medium" => Color.Red,
            "high" => Color.DarkRed,
            "critical" => Color.DarkRed,
            _ => Color.Red
        };

        var embed = new EmbedBuilder()
            .WithTitle("🚨 Trading System Error")
            .WithDescription($"An error occurred in the trading system during: **{context}**")
            .WithColor(color)
            .WithTimestamp(DateTimeOffset.UtcNow);

        embed.AddField("🔍 Error Type", exception.GetType().Name, true);
        embed.AddField("⚠️ Severity", severity, true);
        embed.AddField("📍 Context", context, true);

        // Truncate message if too long for Discord
        var errorMessage = exception.Message;
        if (errorMessage.Length > 1000)
        {
            errorMessage = errorMessage.Substring(0, 997) + "...";
        }
        embed.AddField("📝 Error Message", errorMessage, false);

        // Add inner exception if present
        if (exception.InnerException != null)
        {
            var innerMessage = exception.InnerException.Message;
            if (innerMessage.Length > 500)
            {
                innerMessage = innerMessage.Substring(0, 497) + "...";
            }
            embed.AddField("🔗 Inner Exception", innerMessage, false);
        }

        // Add timestamp and system info
        embed.AddField("🕒 Occurred At", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC"), true);
        embed.AddField("🖥️ System", "Zero DTE Trading System", true);

        embed.WithFooter("Trading Error Alert • Zero DTE System");
        return embed.Build();
    }

    private string BuildTradingErrorMessage(Exception exception, string context, string severity)
    {
        var severityEmoji = severity.ToLower() switch
        {
            "low" => "🟡",
            "medium" => "🔴",
            "high" => "🚨",
            "critical" => "💀",
            _ => "🔴"
        };

        var message = $"{severityEmoji} **Trading System Error - {severity} Severity**\n\n";
        message += $"**Context:** {context}\n";
        message += $"**Error Type:** {exception.GetType().Name}\n";
        message += $"**Time:** {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss UTC}\n\n";

        // Truncate error message if too long
        var errorMessage = exception.Message;
        if (errorMessage.Length > 800)
        {
            errorMessage = errorMessage.Substring(0, 797) + "...";
        }
        message += $"**Error Details:**\n```\n{errorMessage}\n```\n";

        // Add inner exception if present
        if (exception.InnerException != null)
        {
            var innerMessage = exception.InnerException.Message;
            if (innerMessage.Length > 300)
            {
                innerMessage = innerMessage.Substring(0, 297) + "...";
            }
            message += $"**Inner Exception:** {innerMessage}\n";
        }

        message += "\n*Please check logs for full details and stack trace.*";
        return message;
    }
}
