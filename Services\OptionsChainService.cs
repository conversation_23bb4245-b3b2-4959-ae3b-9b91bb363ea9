using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IOptionsChainService
{
    Task<OptionChain> GetOptionChainAsync(string symbol, DateTime expirationDate, decimal underlyingPrice);
    Task<OptionChain> GetOptionsChainAsync(string symbol, DateTime expirationDate, decimal underlyingPrice); // Added missing method
    Task<List<OptionChain>> GetZeroDteOptionChainsAsync(List<string> symbols, Dictionary<string, decimal> underlyingPrices);
    Task<List<OptionContract>> BuildOptionContractsAsync(string underlyingSymbol, decimal underlyingPrice, List<PolygonOptionsContract> polygonContracts);
    Task<decimal> CalculateOptionPriceAsync(OptionContract contract, decimal underlyingPrice, decimal volatility, decimal riskFreeRate);
}

public class OptionsChainService : IOptionsChainService
{
    private readonly IPolygonDataService _polygonService;
    private readonly IOptionsPricingService _pricingService;
    private readonly ILogger<OptionsChainService> _logger;

    public OptionsChainService(
        IPolygonDataService polygonService,
        IOptionsPricingService pricingService,
        ILogger<OptionsChainService> logger)
    {
        _polygonService = polygonService;
        _pricingService = pricingService;
        _logger = logger;
    }

    public async Task<OptionChain> GetOptionChainAsync(string symbol, DateTime expirationDate, decimal underlyingPrice)
    {
        try
        {
            _logger.LogInformation($"Building option chain for {symbol} expiring {expirationDate:yyyy-MM-dd} at price ${underlyingPrice:F2}");

            if (underlyingPrice <= 0)
            {
                _logger.LogWarning($"Invalid underlying price for {symbol}: ${underlyingPrice:F2}");
                return new OptionChain { UnderlyingSymbol = symbol, ExpirationDate = expirationDate };
            }

            // Get options contracts from Polygon
            var polygonContracts = await _polygonService.GetOptionsContractsForDateAsync(symbol, expirationDate);
            if (!polygonContracts.Any())
            {
                _logger.LogWarning($"No options contracts found for {symbol} on {expirationDate:yyyy-MM-dd}");
                return new OptionChain { UnderlyingSymbol = symbol, ExpirationDate = expirationDate, UnderlyingPrice = underlyingPrice };
            }

            // Build option contracts with pricing
            var optionContracts = await BuildOptionContractsAsync(symbol, underlyingPrice, polygonContracts);

            // Create option chain
            var optionChain = new OptionChain
            {
                UnderlyingSymbol = symbol,
                UnderlyingPrice = underlyingPrice,
                ExpirationDate = expirationDate,
                LastUpdated = DateTime.UtcNow,
                Options = optionContracts
            };

            // Separate calls and puts
            optionChain.Calls = optionContracts.Where(o => o.OptionType == OptionType.Call).ToList();
            optionChain.Puts = optionContracts.Where(o => o.OptionType == OptionType.Put).ToList();

            // Calculate chain-level metrics
            optionChain.Volume = optionContracts.Sum(o => (int)o.Volume);
            optionChain.OpenInterest = optionContracts.Sum(o => (int)o.OpenInterest);
            optionChain.ImpliedVolatility = CalculateAverageImpliedVolatility(optionContracts);

            _logger.LogInformation($"Built option chain for {symbol}: {optionChain.Calls.Count} calls, {optionChain.Puts.Count} puts");

            return optionChain;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error building option chain for {symbol} on {expirationDate:yyyy-MM-dd}");
            return new OptionChain { UnderlyingSymbol = symbol, ExpirationDate = expirationDate };
        }
    }

    public async Task<OptionChain> GetOptionsChainAsync(string symbol, DateTime expirationDate, decimal underlyingPrice)
    {
        // Alias for GetOptionChainAsync to satisfy interface requirements
        return await GetOptionChainAsync(symbol, expirationDate, underlyingPrice);
    }

    public async Task<List<OptionChain>> GetZeroDteOptionChainsAsync(List<string> symbols, Dictionary<string, decimal> underlyingPrices)
    {
        var chains = new List<OptionChain>();
        var today = DateTime.Today;

        foreach (var symbol in symbols)
        {
            try
            {
                // Get underlying price for this symbol
                if (!underlyingPrices.TryGetValue(symbol, out var underlyingPrice) || underlyingPrice <= 0)
                {
                    _logger.LogWarning($"No valid underlying price provided for {symbol}");
                    continue;
                }

                // Get all options contracts for the symbol
                var allContracts = await _polygonService.GetOptionsContractsAsync(symbol);

                // Filter for today's expiration (0 DTE)
                var todayContracts = allContracts
                    .Where(c => c.ExpirationDateTime.Date == today)
                    .ToList();

                if (todayContracts.Any())
                {
                    var chain = await GetOptionChainAsync(symbol, today, underlyingPrice);
                    if (chain.Options.Any())
                    {
                        chains.Add(chain);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting 0 DTE options for {symbol}");
            }
        }

        _logger.LogInformation($"Found {chains.Count} symbols with 0 DTE options");
        return chains;
    }

    public async Task<List<OptionContract>> BuildOptionContractsAsync(string underlyingSymbol, decimal underlyingPrice, List<PolygonOptionsContract> polygonContracts)
    {
        var contracts = new List<OptionContract>();
        var volatility = await EstimateImpliedVolatilityAsync(underlyingSymbol);
        var riskFreeRate = 0.05m; // 5% default risk-free rate

        foreach (var polygonContract in polygonContracts)
        {
            try
            {
                var optionType = polygonContract.IsCall ? OptionType.Call : OptionType.Put;
                
                var contract = new OptionContract
                {
                    Symbol = polygonContract.Ticker,
                    UnderlyingSymbol = underlyingSymbol,
                    ExpirationDate = polygonContract.ExpirationDateTime,
                    StrikePrice = polygonContract.StrikePrice,
                    OptionType = optionType,
                    LastUpdated = DateTime.UtcNow
                };

                // Calculate theoretical price using Black-Scholes
                var theoreticalPrice = await CalculateOptionPriceAsync(contract, underlyingPrice, volatility, riskFreeRate);
                
                // Set bid/ask around theoretical price (since we don't have real-time quotes)
                var spread = Math.Max(0.05m, theoreticalPrice * 0.02m); // 2% spread minimum $0.05
                contract.Bid = Math.Max(0.01m, theoreticalPrice - spread / 2);
                contract.Ask = theoreticalPrice + spread / 2;
                contract.LastPrice = theoreticalPrice;

                // Calculate Greeks
                var greeks = await _pricingService.CalculateGreeksAsync(contract, underlyingPrice, volatility, riskFreeRate);
                contract.Delta = greeks.Delta;
                contract.Gamma = greeks.Gamma;
                contract.Theta = greeks.Theta;
                contract.Vega = greeks.Vega;
                contract.ImpliedVolatility = volatility;

                // Estimate volume and open interest based on moneyness
                var moneyness = Math.Abs(contract.StrikePrice - underlyingPrice) / underlyingPrice;
                contract.Volume = EstimateVolume(moneyness);
                contract.OpenInterest = EstimateOpenInterest(moneyness);

                contracts.Add(contract);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"Error building option contract {polygonContract.Ticker}");
            }
        }

        return contracts;
    }

    public async Task<decimal> CalculateOptionPriceAsync(OptionContract contract, decimal underlyingPrice, decimal volatility, decimal riskFreeRate)
    {
        return await _pricingService.CalculateBlackScholesPrice(
            underlyingPrice,
            contract.StrikePrice,
            contract.DaysToExpiration / 365.25m,
            riskFreeRate,
            volatility,
            contract.OptionType == OptionType.Call);
    }

    private async Task<decimal> EstimateImpliedVolatilityAsync(string symbol)
    {
        try
        {
            // Try to get VIX for market-wide volatility
            var vix = await _polygonService.GetCurrentVixAsync();
            if (vix > 0)
            {
                // Convert VIX to decimal volatility (VIX is in percentage points)
                return vix / 100m;
            }

            // Fallback: estimate based on symbol
            return symbol.ToUpper() switch
            {
                "SPY" => 0.20m,  // 20% for SPY
                "QQQ" => 0.25m,  // 25% for QQQ
                "IWM" => 0.30m,  // 30% for IWM
                _ => 0.25m       // 25% default
            };
        }
        catch
        {
            return 0.25m; // 25% default volatility
        }
    }

    private decimal EstimateVolume(decimal moneyness)
    {
        // Higher volume for ATM options, lower for OTM
        if (moneyness < 0.02m) return 1000; // ATM
        if (moneyness < 0.05m) return 500;  // Near ATM
        if (moneyness < 0.10m) return 200;  // Moderate OTM
        return 50; // Deep OTM
    }

    private decimal EstimateOpenInterest(decimal moneyness)
    {
        // Similar pattern to volume but generally higher numbers
        if (moneyness < 0.02m) return 5000; // ATM
        if (moneyness < 0.05m) return 2500; // Near ATM
        if (moneyness < 0.10m) return 1000; // Moderate OTM
        return 250; // Deep OTM
    }

    private decimal CalculateAverageImpliedVolatility(List<OptionContract> contracts)
    {
        if (!contracts.Any()) return 0.25m;

        var validContracts = contracts.Where(c => c.ImpliedVolatility > 0).ToList();
        if (!validContracts.Any()) return 0.25m;

        return validContracts.Average(c => c.ImpliedVolatility);
    }
}
