using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IMarketRegimeAnalyzer
{
    Task<MarketRegime> GetCurrentRegimeAsync();
    Task<decimal> GetVixAsync();
    Task<MarketTrend> GetMarketTrendAsync(string symbol);
    Task<bool> IsGoodTradingEnvironmentAsync();
    Task<List<string>> GetRecommendedStrategiesAsync();

    // Enhanced market analysis methods
    Task<VolatilityForecast> GetVolatilityForecastAsync(string symbol, int daysAhead = 5);
    Task<MarketMicrostructure> GetMarketMicrostructureAsync(string symbol);
    Task<RegimeTransitionProbability> GetRegimeTransitionProbabilityAsync();
    Task<MarketStressIndicators> GetMarketStressIndicatorsAsync();
    Task<VolatilitySurface> GetVolatilitySurfaceAsync(string symbol);
    Task<MultiTimeframeAnalysis> GetMultiTimeframeAnalysisAsync(string symbol);

    // Phase 1 Enhanced Market Analysis
    Task<MarketSentimentAnalysis> GetMarketSentimentAsync(string symbol);
    Task<OptionsFlowAnalysis> GetOptionsFlowAnalysisAsync(string symbol);
    Task<VolatilityRegimeDetection> GetEnhancedVolatilityRegimeAsync(string symbol);
    Task<MarketBreadthIndicators> GetMarketBreadthAsync();
    Task<CorrelationBreakdownAlert> CheckCorrelationBreakdownAsync(List<string> symbols);
    Task<VolatilitySpikeAlert> CheckVolatilitySpikeAsync(string symbol);
    Task<List<UnusualOptionsActivity>> GetUnusualOptionsActivityAsync(List<string> symbols);
}

public class MarketRegimeAnalyzer : IMarketRegimeAnalyzer
{
    private readonly ILogger<MarketRegimeAnalyzer> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;
    private readonly IPolygonDataService _polygonDataService;
    private readonly IAlpacaVixService _alpacaVixService;
    private readonly IHighPerformanceCacheService _cache;

    public MarketRegimeAnalyzer(
        ILogger<MarketRegimeAnalyzer> logger,
        IConfiguration configuration,
        IAlpacaService alpacaService,
        IPolygonDataService polygonDataService,
        IAlpacaVixService alpacaVixService,
        IHighPerformanceCacheService cache)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
        _polygonDataService = polygonDataService;
        _alpacaVixService = alpacaVixService;
        _cache = cache;
    }

    public async Task<MarketRegime> GetCurrentRegimeAsync()
    {
        try
        {
            var vix = await GetVixAsync();
            var spxTrend = await GetMarketTrendAsync("SPX");
            
            var vixLow = _configuration.GetValue<decimal>("MarketRegime:VixLowThreshold", 20);
            var vixHigh = _configuration.GetValue<decimal>("MarketRegime:VixHighThreshold", 30);

            var regime = new MarketRegime
            {
                Vix = vix,
                Trend = spxTrend,
                Timestamp = DateTime.UtcNow
            };

            if (vix < vixLow)
            {
                regime.VolatilityRegime = VolatilityRegime.Low;
                regime.Description = "Low volatility environment - favorable for premium selling";
            }
            else if (vix > vixHigh)
            {
                regime.VolatilityRegime = VolatilityRegime.High;
                regime.Description = "High volatility environment - avoid trading or use defensive strategies";
            }
            else
            {
                regime.VolatilityRegime = VolatilityRegime.Medium;
                regime.Description = "Medium volatility environment - selective trading";
            }

            // Determine overall regime
            if (regime.VolatilityRegime == VolatilityRegime.Low && spxTrend == MarketTrend.Bullish)
            {
                regime.OverallRegime = "Ideal - Low Vol Bull Market";
                regime.Confidence = 0.9m;
            }
            else if (regime.VolatilityRegime == VolatilityRegime.Medium && spxTrend != MarketTrend.Bearish)
            {
                regime.OverallRegime = "Good - Medium Vol Neutral/Bull";
                regime.Confidence = 0.7m;
            }
            else if (regime.VolatilityRegime == VolatilityRegime.High)
            {
                regime.OverallRegime = "Poor - High Volatility";
                regime.Confidence = 0.3m;
            }
            else
            {
                regime.OverallRegime = "Neutral";
                regime.Confidence = 0.5m;
            }

            _logger.LogInformation($"Market Regime: {regime.OverallRegime} (SyntheticVIX: {vix:F1}, Trend: {spxTrend})");
            return regime;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing market regime");
            return new MarketRegime
            {
                VolatilityRegime = VolatilityRegime.Unknown,
                Trend = MarketTrend.Unknown,
                OverallRegime = "Unknown",
                Confidence = 0,
                Timestamp = DateTime.UtcNow
            };
        }
    }

    public async Task<decimal> GetVixAsync()
    {
        const string cacheKey = "vix_current";

        try
        {
            // Use high-performance cache with 30-second expiration for SyntheticVIX data
            return await _cache.GetOrSetAsync(cacheKey, async () =>
            {
                // Primary: Use AlpacaVixService which now uses SyntheticVixService with z-score normalization
                _logger.LogDebug("Cache MISS - Getting SyntheticVIX from AlpacaVixService (with Polygon fallback)");
                var vix = await _alpacaVixService.GetCurrentVixAsync();

            if (vix > 0 && vix < 100) // Sanity check
            {
                _logger.LogDebug($"SyntheticVIX from AlpacaVixService: {vix:F2}");
                return vix;
            }

            // Secondary fallback: Calculate VIX-equivalent using multiple methods
            _logger.LogInformation("AlpacaVixService failed, calculating SyntheticVIX-equivalent using historical volatility and market indicators");

            // Method 1: Historical Volatility (primary) - use longer period for better accuracy
            var historicalVol = await CalculateHistoricalVolatilityAsync("SPY",
                _configuration.GetValue<int>("MarketRegime:VolatilityCalculationDays", 30));

            // Method 2: ATM Implied Volatility (if available)
            var impliedVol = await CalculateATMImpliedVolatilityAsync("SPY");

            // Method 3: Price-based volatility indicators
            var atr = await CalculateATRVolatilityAsync("SPY",
                _configuration.GetValue<int>("MarketRegime:ATRPeriods", 14));

            // Method 4: Intraday volatility
            var intradayVol = await CalculateIntradayVolatilityAsync("SPY");

            // Combine methods with weights (HV: 40%, IV: 25%, ATR: 20%, Intraday: 15%)
            decimal vixEquivalent = 0;
            decimal totalWeight = 0;

            if (historicalVol > 0)
            {
                vixEquivalent += historicalVol * 0.4m;
                totalWeight += 0.4m;
            }

            if (impliedVol > 0)
            {
                vixEquivalent += impliedVol * 0.25m;
                totalWeight += 0.25m;
            }

            if (atr > 0)
            {
                vixEquivalent += atr * 0.2m;
                totalWeight += 0.2m;
            }

            if (intradayVol > 0)
            {
                vixEquivalent += intradayVol * 0.15m;
                totalWeight += 0.15m;
            }

            if (totalWeight > 0)
            {
                vixEquivalent = vixEquivalent / totalWeight;
                _logger.LogInformation($"Calculated SyntheticVIX-equivalent: {vixEquivalent:F2} (HV: {historicalVol:F2}, IV: {impliedVol:F2}, ATR: {atr:F2})");
                return vixEquivalent;
            }

                // Fallback if all methods fail
                _logger.LogWarning("All volatility calculation methods failed, using conservative estimate");
                return 20m; // Conservative default
            }, TimeSpan.FromSeconds(30));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SyntheticVIX data");
            return 20m; // Safe default
        }
    }

    public async Task<MarketTrend> GetMarketTrendAsync(string symbol)
    {
        try
        {
            // Enhanced trend analysis using multiple indicators
            var currentPrice = await _alpacaService.GetCurrentPriceAsync(symbol);
            if (currentPrice <= 0)
                return MarketTrend.Unknown;

            // Get historical data for trend analysis
            var lookbackPeriods = _configuration.GetValue<int>("MarketRegime:TrendLookbackPeriods", 20);
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-(lookbackPeriods + 5));

            var historicalData = await _alpacaService.GetHistoricalDataAsync(symbol, startDate, endDate);

            if (historicalData == null || historicalData.Count < lookbackPeriods)
            {
                _logger.LogWarning($"Insufficient data for trend analysis of {symbol}");
                return MarketTrend.Unknown;
            }

            // Calculate Simple Moving Averages
            var recentData = historicalData.TakeLast(lookbackPeriods).ToList();
            var sma5 = recentData.TakeLast(5).Average(d => d.Close);
            var sma10 = recentData.TakeLast(10).Average(d => d.Close);
            var sma20 = recentData.Average(d => d.Close);

            // Price relative to moving averages
            var priceVsSMA5 = (currentPrice - sma5) / sma5;
            var priceVsSMA10 = (currentPrice - sma10) / sma10;
            var priceVsSMA20 = (currentPrice - sma20) / sma20;

            // Moving average alignment
            var smaAlignment = 0;
            if (sma5 > sma10) smaAlignment++;
            if (sma10 > sma20) smaAlignment++;
            if (sma5 > sma20) smaAlignment++;

            // Recent price momentum
            var recentHigh = recentData.TakeLast(5).Max(d => d.High);
            var recentLow = recentData.TakeLast(5).Min(d => d.Low);
            var pricePosition = (currentPrice - recentLow) / (recentHigh - recentLow);

            // Determine trend based on multiple factors
            var bullishSignals = 0;
            var bearishSignals = 0;

            if (priceVsSMA5 > 0.01m) bullishSignals++; else if (priceVsSMA5 < -0.01m) bearishSignals++;
            if (priceVsSMA10 > 0.005m) bullishSignals++; else if (priceVsSMA10 < -0.005m) bearishSignals++;
            if (priceVsSMA20 > 0.002m) bullishSignals++; else if (priceVsSMA20 < -0.002m) bearishSignals++;
            if (smaAlignment >= 2) bullishSignals++; else if (smaAlignment == 0) bearishSignals++;
            if (pricePosition > 0.7m) bullishSignals++; else if (pricePosition < 0.3m) bearishSignals++;

            if (bullishSignals >= 3)
                return MarketTrend.Bullish;
            else if (bearishSignals >= 3)
                return MarketTrend.Bearish;
            else
                return MarketTrend.Neutral;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error analyzing trend for {symbol}");
            return MarketTrend.Unknown;
        }
    }

    public async Task<bool> IsGoodTradingEnvironmentAsync()
    {
        try
        {
            var regime = await GetCurrentRegimeAsync();

            // Check if high volatility trading is allowed
            var allowHighVolTrading = _configuration.GetValue<bool>("MarketRegime:AllowHighVolatilityTrading", false);

            if (regime.VolatilityRegime == VolatilityRegime.High)
            {
                if (!allowHighVolTrading)
                {
                    _logger.LogInformation("High volatility environment - trading disabled");
                    return false;
                }
                else
                {
                    _logger.LogInformation("High volatility environment - trading with reduced risk");
                    // Continue with additional checks for high volatility trading
                }
            }

            // Check if within optimal trading hours
            var now = DateTime.Now.TimeOfDay;
            var entryStart = TimeSpan.Parse(_configuration["Trading:EntryTimeStart"] ?? "09:45:00");
            var entryEnd = TimeSpan.Parse(_configuration["Trading:EntryTimeEnd"] ?? "10:30:00");
            var managementTime = TimeSpan.Parse(_configuration["Trading:ManagementTime"] ?? "14:00:00");
            var forceClose = TimeSpan.Parse(_configuration["Trading:ForceCloseTime"] ?? "15:45:00");

            // Allow entry only during entry window
            if (now >= entryStart && now <= entryEnd)
            {
                // Higher confidence required for high volatility
                var requiredConfidence = regime.VolatilityRegime == VolatilityRegime.High ? 0.75m : 0.6m;
                var isConfident = regime.Confidence >= requiredConfidence;

                if (regime.VolatilityRegime == VolatilityRegime.High && isConfident)
                {
                    _logger.LogInformation($"High volatility trading approved - confidence: {regime.Confidence:P1}");
                }

                return isConfident;
            }

            // Allow management but no new entries after entry window
            if (now > entryEnd && now < forceClose)
            {
                return false; // No new entries, only management
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking trading environment");
            return false;
        }
    }

    public async Task<List<string>> GetRecommendedStrategiesAsync()
    {
        try
        {
            var regime = await GetCurrentRegimeAsync();
            var strategies = new List<string>();

            switch (regime.VolatilityRegime)
            {
                case VolatilityRegime.Low:
                    if (regime.Trend == MarketTrend.Bullish)
                    {
                        strategies.Add("PutCreditSpread");
                        strategies.Add("IronButterfly");
                    }
                    else if (regime.Trend == MarketTrend.Bearish)
                    {
                        strategies.Add("CallCreditSpread");
                    }
                    else
                    {
                        strategies.Add("IronButterfly");
                        strategies.Add("PutCreditSpread");
                    }
                    break;

                case VolatilityRegime.Medium:
                    if (regime.Trend == MarketTrend.Bullish)
                    {
                        strategies.Add("PutCreditSpread");
                    }
                    else if (regime.Trend == MarketTrend.Bearish)
                    {
                        strategies.Add("CallCreditSpread");
                    }
                    break;

                case VolatilityRegime.High:
                    // Check if high volatility trading is enabled
                    var allowHighVolTrading = _configuration.GetValue<bool>("MarketRegime:AllowHighVolatilityTrading", false);
                    if (allowHighVolTrading)
                    {
                        _logger.LogInformation("High volatility - recommending conservative strategies");
                        // In high volatility, prefer strategies that benefit from volatility
                        strategies.Add("IronButterfly"); // Benefits from high IV
                        if (regime.Trend == MarketTrend.Neutral || regime.Trend == MarketTrend.Unknown)
                        {
                            strategies.Add("IronCondor"); // Range-bound strategy for uncertain direction
                        }
                    }
                    else
                    {
                        _logger.LogInformation("High volatility - no strategies recommended (disabled)");
                    }
                    break;
            }

            return strategies;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recommended strategies");
            return new List<string>();
        }
    }

    // Enhanced Market Analysis Implementation
    public async Task<VolatilityForecast> GetVolatilityForecastAsync(string symbol, int daysAhead = 5)
    {
        try
        {
            _logger.LogInformation($"Generating volatility forecast for {symbol}, {daysAhead} days ahead");

            var forecast = new VolatilityForecast
            {
                Symbol = symbol,
                ForecastDate = DateTime.UtcNow,
                DaysAhead = daysAhead,
                Model = "Enhanced GARCH-like"
            };

            // Get historical data for volatility modeling
            var lookbackDays = _configuration.GetValue<int>("MarketRegime:VolatilityForecastLookback", 60);
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-lookbackDays);

            var historicalData = await _alpacaService.GetHistoricalDataAsync(symbol, startDate, endDate);
            if (historicalData == null || historicalData.Count < 30)
            {
                _logger.LogWarning($"Insufficient data for volatility forecast of {symbol}");
                return forecast;
            }

            // Calculate returns
            var returns = new List<decimal>();
            for (int i = 1; i < historicalData.Count; i++)
            {
                var dailyReturn = (historicalData[i].Close - historicalData[i - 1].Close) / historicalData[i - 1].Close;
                returns.Add(dailyReturn);
            }

            // Current volatility
            forecast.CurrentVolatility = await CalculateHistoricalVolatilityAsync(symbol, 20);

            // GARCH-like volatility forecasting
            var garchForecast = CalculateGarchLikeForecast(returns, daysAhead);
            forecast.ForecastedVolatility = garchForecast.ForecastedVol;
            forecast.VolatilityTrend = garchForecast.Trend;
            forecast.Confidence = garchForecast.Confidence;
            forecast.VolatilityPath = garchForecast.VolatilityPath;
            forecast.ModelParameters = garchForecast.Parameters;

            _logger.LogInformation($"Volatility forecast for {symbol}: Current={forecast.CurrentVolatility:F2}%, Forecast={forecast.ForecastedVolatility:F2}%, Trend={forecast.VolatilityTrend:F3}");

            return forecast;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error generating volatility forecast for {symbol}");
            return new VolatilityForecast { Symbol = symbol, ForecastDate = DateTime.UtcNow, DaysAhead = daysAhead };
        }
    }

    private (decimal ForecastedVol, decimal Trend, decimal Confidence, List<decimal> VolatilityPath, Dictionary<string, decimal> Parameters)
        CalculateGarchLikeForecast(List<decimal> returns, int daysAhead)
    {
        try
        {
            // GARCH-like parameters (simplified implementation)
            var alpha = 0.1m; // Weight for recent squared returns
            var beta = 0.85m;  // Weight for previous variance
            var omega = 0.00001m; // Long-term variance

            var volatilityPath = new List<decimal>();
            var parameters = new Dictionary<string, decimal>
            {
                ["alpha"] = alpha,
                ["beta"] = beta,
                ["omega"] = omega
            };

            // Calculate initial variance
            var recentReturns = returns.TakeLast(20).ToList();
            var meanReturn = recentReturns.Average();
            var initialVariance = recentReturns.Select(r => (r - meanReturn) * (r - meanReturn)).Average();

            var currentVariance = initialVariance;
            var volatilitySum = 0m;

            // Forecast volatility for each day
            for (int i = 0; i < daysAhead; i++)
            {
                // GARCH(1,1) equation: σ²(t+1) = ω + α*ε²(t) + β*σ²(t)
                var lastReturn = returns.LastOrDefault();
                var lastSquaredReturn = lastReturn * lastReturn;

                currentVariance = omega + alpha * lastSquaredReturn + beta * currentVariance;
                var dailyVol = (decimal)Math.Sqrt((double)currentVariance) * (decimal)Math.Sqrt(252) * 100; // Annualized %

                volatilityPath.Add(dailyVol);
                volatilitySum += dailyVol;
            }

            var forecastedVol = volatilitySum / daysAhead;
            var currentVol = (decimal)Math.Sqrt((double)initialVariance) * (decimal)Math.Sqrt(252) * 100;
            var trend = (forecastedVol - currentVol) / Math.Max(currentVol, 1m);

            // Calculate confidence based on model stability
            var volatilityStdDev = volatilityPath.Count > 1 ?
                (decimal)Math.Sqrt((double)volatilityPath.Select(v => (v - forecastedVol) * (v - forecastedVol)).Average()) : 0;
            var confidence = Math.Max(0.3m, Math.Min(0.95m, 1m - (volatilityStdDev / Math.Max(forecastedVol, 1m))));

            return (forecastedVol, trend, confidence, volatilityPath, parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GARCH-like forecast calculation");
            return (20m, 0m, 0.5m, new List<decimal>(), new Dictionary<string, decimal>());
        }
    }

    public async Task<MarketMicrostructure> GetMarketMicrostructureAsync(string symbol)
    {
        try
        {
            _logger.LogInformation($"Analyzing market microstructure for {symbol}");

            var microstructure = new MarketMicrostructure
            {
                Symbol = symbol,
                Timestamp = DateTime.UtcNow
            };

            // Get current price and volume data
            var currentPrice = await _alpacaService.GetCurrentPriceAsync(symbol);
            if (currentPrice <= 0)
            {
                _logger.LogWarning($"Could not get current price for {symbol}");
                return microstructure;
            }

            // Get recent intraday data for microstructure analysis
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddHours(-6); // Last 6 hours of trading

            var intradayData = await _alpacaService.GetHistoricalDataAsync(symbol, startDate, endDate);
            if (intradayData == null || intradayData.Count < 10)
            {
                _logger.LogWarning($"Insufficient intraday data for microstructure analysis of {symbol}");
                return microstructure;
            }

            // Calculate VWAP
            var totalVolume = intradayData.Sum(d => d.Volume);
            var vwap = totalVolume > 0 ? intradayData.Sum(d => d.Close * d.Volume) / totalVolume : currentPrice;
            microstructure.VolumeWeightedAveragePrice = vwap;

            // Calculate bid-ask spread approximation (using high-low as proxy)
            var recentData = intradayData.TakeLast(10).ToList();
            var avgSpread = recentData.Average(d => d.High - d.Low);
            microstructure.BidAskSpread = avgSpread / currentPrice; // As percentage

            // Calculate order flow imbalance (volume-weighted price momentum)
            var volumeWeightedReturns = new List<decimal>();
            for (int i = 1; i < intradayData.Count; i++)
            {
                var priceChange = (intradayData[i].Close - intradayData[i - 1].Close) / intradayData[i - 1].Close;
                var volumeWeight = intradayData[i].Volume / Math.Max(totalVolume, 1);
                volumeWeightedReturns.Add(priceChange * volumeWeight);
            }
            microstructure.OrderFlowImbalance = volumeWeightedReturns.Sum();

            // Calculate market impact (price impact per unit volume)
            var volumeChanges = new List<decimal>();
            var priceChanges = new List<decimal>();
            for (int i = 1; i < intradayData.Count; i++)
            {
                var volumeChange = intradayData[i].Volume - intradayData[i - 1].Volume;
                var priceChange = Math.Abs(intradayData[i].Close - intradayData[i - 1].Close);
                if (volumeChange > 0)
                {
                    volumeChanges.Add(volumeChange);
                    priceChanges.Add(priceChange);
                }
            }
            microstructure.MarketImpact = volumeChanges.Count > 0 ?
                priceChanges.Zip(volumeChanges, (p, v) => p / Math.Max(v, 1)).Average() : 0;

            // Calculate liquidity score (inverse of spread and impact)
            microstructure.LiquidityScore = Math.Max(0, Math.Min(100,
                100 * (1 - microstructure.BidAskSpread) * (1 - Math.Min(microstructure.MarketImpact * 1000, 1))));

            // Calculate momentum score
            var shortTermReturns = intradayData.TakeLast(5).ToList();
            var longTermReturns = intradayData.TakeLast(20).ToList();
            var shortMomentum = shortTermReturns.Count > 1 ?
                (shortTermReturns.Last().Close - shortTermReturns.First().Close) / shortTermReturns.First().Close : 0;
            var longMomentum = longTermReturns.Count > 1 ?
                (longTermReturns.Last().Close - longTermReturns.First().Close) / longTermReturns.First().Close : 0;
            microstructure.MomentumScore = (shortMomentum * 0.7m + longMomentum * 0.3m) * 100;

            // Add technical indicators
            microstructure.TechnicalIndicators["RSI"] = await CalculateRSI(intradayData, 14);
            microstructure.TechnicalIndicators["MACD"] = await CalculateMACD(intradayData);
            microstructure.TechnicalIndicators["BollingerPosition"] = await CalculateBollingerPosition(intradayData, currentPrice);

            _logger.LogInformation($"Microstructure analysis for {symbol}: Liquidity={microstructure.LiquidityScore:F1}, Momentum={microstructure.MomentumScore:F2}%, VWAP={microstructure.VolumeWeightedAveragePrice:F2}");

            return microstructure;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error analyzing market microstructure for {symbol}");
            return new MarketMicrostructure { Symbol = symbol, Timestamp = DateTime.UtcNow };
        }
    }

    private async Task<decimal> CalculateRSI(List<AlpacaHistoricalBar> data, int periods)
    {
        try
        {
            if (data.Count < periods + 1) return 50; // Neutral RSI

            var gains = new List<decimal>();
            var losses = new List<decimal>();

            for (int i = 1; i < data.Count; i++)
            {
                var change = data[i].Close - data[i - 1].Close;
                gains.Add(Math.Max(change, 0));
                losses.Add(Math.Max(-change, 0));
            }

            var avgGain = gains.TakeLast(periods).Average();
            var avgLoss = losses.TakeLast(periods).Average();

            if (avgLoss == 0) return 100;
            var rs = avgGain / avgLoss;
            return 100 - (100 / (1 + rs));
        }
        catch
        {
            return 50; // Neutral RSI on error
        }
    }

    private async Task<decimal> CalculateMACD(List<AlpacaHistoricalBar> data)
    {
        try
        {
            if (data.Count < 26) return 0;

            var prices = data.Select(d => d.Close).ToList();
            var ema12 = CalculateEMA(prices, 12);
            var ema26 = CalculateEMA(prices, 26);
            return ema12 - ema26;
        }
        catch
        {
            return 0;
        }
    }

    private decimal CalculateEMA(List<decimal> prices, int periods)
    {
        if (prices.Count < periods) return prices.LastOrDefault();

        var multiplier = 2m / (periods + 1);
        var ema = prices.Take(periods).Average(); // Start with SMA

        for (int i = periods; i < prices.Count; i++)
        {
            ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
        }

        return ema;
    }

    private async Task<decimal> CalculateBollingerPosition(List<AlpacaHistoricalBar> data, decimal currentPrice)
    {
        try
        {
            if (data.Count < 20) return 0.5m; // Middle position

            var prices = data.TakeLast(20).Select(d => d.Close).ToList();
            var sma = prices.Average();
            var stdDev = (decimal)Math.Sqrt((double)prices.Select(p => (p - sma) * (p - sma)).Average());

            var upperBand = sma + (2 * stdDev);
            var lowerBand = sma - (2 * stdDev);

            if (upperBand == lowerBand) return 0.5m;
            return (currentPrice - lowerBand) / (upperBand - lowerBand);
        }
        catch
        {
            return 0.5m;
        }
    }

    public async Task<RegimeTransitionProbability> GetRegimeTransitionProbabilityAsync()
    {
        try
        {
            _logger.LogInformation("Analyzing regime transition probabilities");

            var transition = new RegimeTransitionProbability
            {
                AnalysisDate = DateTime.UtcNow
            };

            // Get current regime
            var currentRegime = await GetCurrentRegimeAsync();
            transition.CurrentRegime = currentRegime.VolatilityRegime;

            // Analyze historical regime changes
            var regimeHistory = await GetRegimeHistoryAsync(30); // Last 30 days
            transition.DaysInCurrentRegime = CalculateDaysInCurrentRegime(regimeHistory, transition.CurrentRegime);

            // Calculate transition probabilities based on historical patterns
            var transitionMatrix = CalculateTransitionMatrix(regimeHistory);
            transition.TransitionProbabilities = transitionMatrix.GetValueOrDefault(transition.CurrentRegime, new Dictionary<VolatilityRegime, decimal>());

            // Calculate regime stability
            transition.RegimeStability = CalculateRegimeStability(regimeHistory, transition.CurrentRegime);

            // Calculate transition signal strength
            transition.TransitionSignal = await CalculateTransitionSignal(currentRegime);

            _logger.LogInformation($"Regime transition analysis: Current={transition.CurrentRegime}, Stability={transition.RegimeStability:F2}, Days in regime={transition.DaysInCurrentRegime}");

            return transition;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing regime transition probabilities");
            return new RegimeTransitionProbability { AnalysisDate = DateTime.UtcNow };
        }
    }

    public async Task<MultiTimeframeAnalysis> GetMultiTimeframeAnalysisAsync(string symbol)
    {
        try
        {
            _logger.LogInformation($"Performing multi-timeframe analysis for {symbol}");

            var analysis = new MultiTimeframeAnalysis
            {
                Symbol = symbol,
                Timestamp = DateTime.UtcNow
            };

            // Define timeframes to analyze
            var timeframes = new Dictionary<string, int>
            {
                ["1m"] = 1,
                ["5m"] = 5,
                ["15m"] = 15,
                ["1h"] = 60,
                ["1d"] = 1440
            };

            var trendStrengths = new List<decimal>();
            var momentumScores = new List<decimal>();

            foreach (var timeframe in timeframes)
            {
                var trendAnalysis = await AnalyzeTimeframeTrend(symbol, timeframe.Key, timeframe.Value);
                analysis.TimeframeTrends[timeframe.Key] = trendAnalysis;

                trendStrengths.Add(trendAnalysis.Strength);
                momentumScores.Add(trendAnalysis.Momentum);
            }

            // Calculate overall metrics
            analysis.OverallMomentum = momentumScores.Average();
            analysis.TrendAlignment = CalculateTrendAlignment(analysis.TimeframeTrends);
            analysis.DominantTimeframe = GetDominantTimeframe(analysis.TimeframeTrends);
            analysis.SignalStrength = CalculateSignalStrength(analysis.TimeframeTrends, analysis.TrendAlignment);

            _logger.LogInformation($"Multi-timeframe analysis for {symbol}: Alignment={analysis.TrendAlignment:F2}, Dominant={analysis.DominantTimeframe}, Signal={analysis.SignalStrength:F2}");

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error in multi-timeframe analysis for {symbol}");
            return new MultiTimeframeAnalysis { Symbol = symbol, Timestamp = DateTime.UtcNow };
        }
    }

    private async Task<List<(DateTime Date, VolatilityRegime Regime)>> GetRegimeHistoryAsync(int days)
    {
        var history = new List<(DateTime Date, VolatilityRegime Regime)>();

        try
        {
            // Simulate regime history based on VIX levels (in real implementation, this would be stored)
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.AddDays(-days);

            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                // For simulation, use a simple pattern - in reality, you'd store this data
                var vix = await GetVixAsync(); // This would be historical VIX for the date
                var regime = vix < 20 ? VolatilityRegime.Low :
                           vix > 30 ? VolatilityRegime.High : VolatilityRegime.Medium;
                history.Add((date, regime));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting regime history");
        }

        return history;
    }

    private int CalculateDaysInCurrentRegime(List<(DateTime Date, VolatilityRegime Regime)> history, VolatilityRegime currentRegime)
    {
        var days = 0;
        for (int i = history.Count - 1; i >= 0; i--)
        {
            if (history[i].Regime == currentRegime)
                days++;
            else
                break;
        }
        return days;
    }

    private Dictionary<VolatilityRegime, Dictionary<VolatilityRegime, decimal>> CalculateTransitionMatrix(
        List<(DateTime Date, VolatilityRegime Regime)> history)
    {
        var matrix = new Dictionary<VolatilityRegime, Dictionary<VolatilityRegime, decimal>>();
        var transitions = new Dictionary<VolatilityRegime, Dictionary<VolatilityRegime, int>>();

        // Initialize counters
        foreach (VolatilityRegime from in Enum.GetValues<VolatilityRegime>())
        {
            transitions[from] = new Dictionary<VolatilityRegime, int>();
            foreach (VolatilityRegime to in Enum.GetValues<VolatilityRegime>())
            {
                transitions[from][to] = 0;
            }
        }

        // Count transitions
        for (int i = 1; i < history.Count; i++)
        {
            var from = history[i - 1].Regime;
            var to = history[i].Regime;
            transitions[from][to]++;
        }

        // Convert to probabilities
        foreach (var from in transitions.Keys)
        {
            matrix[from] = new Dictionary<VolatilityRegime, decimal>();
            var total = transitions[from].Values.Sum();

            foreach (var to in transitions[from].Keys)
            {
                matrix[from][to] = total > 0 ? (decimal)transitions[from][to] / total : 0;
            }
        }

        return matrix;
    }

    private decimal CalculateRegimeStability(List<(DateTime Date, VolatilityRegime Regime)> history, VolatilityRegime currentRegime)
    {
        if (history.Count < 2) return 0.5m;

        var recentHistory = history.TakeLast(10).ToList();
        var sameRegimeCount = recentHistory.Count(h => h.Regime == currentRegime);
        return (decimal)sameRegimeCount / recentHistory.Count;
    }

    private async Task<decimal> CalculateTransitionSignal(MarketRegime currentRegime)
    {
        try
        {
            // Calculate signal strength based on multiple factors
            var vixTrend = await CalculateVixTrend();
            var marketStress = await GetMarketStressLevel();
            var volatilityMomentum = await CalculateVolatilityMomentum();

            // Combine signals (higher values indicate higher probability of regime change)
            var signal = (Math.Abs(vixTrend) * 0.4m) + (marketStress * 0.3m) + (Math.Abs(volatilityMomentum) * 0.3m);
            return Math.Min(1m, signal);
        }
        catch
        {
            return 0.5m; // Neutral signal on error
        }
    }

    private async Task<TrendAnalysis> AnalyzeTimeframeTrend(string symbol, string timeframe, int minutes)
    {
        try
        {
            var analysis = new TrendAnalysis();

            // Get data for the specific timeframe
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddMinutes(-minutes * 50); // Get enough data points

            var data = await _alpacaService.GetHistoricalDataAsync(symbol, startDate, endDate);
            if (data == null || data.Count < 10)
            {
                return new TrendAnalysis { Trend = MarketTrend.Unknown, Strength = 0.5m };
            }

            // Calculate trend indicators
            var prices = data.Select(d => d.Close).ToList();
            var currentPrice = prices.Last();

            // Simple trend analysis
            var sma10 = prices.TakeLast(Math.Min(10, prices.Count)).Average();
            var sma20 = prices.TakeLast(Math.Min(20, prices.Count)).Average();

            // Determine trend
            if (currentPrice > sma10 && sma10 > sma20)
            {
                analysis.Trend = MarketTrend.Bullish;
                analysis.Strength = Math.Min(1m, (currentPrice - sma20) / sma20 * 10);
            }
            else if (currentPrice < sma10 && sma10 < sma20)
            {
                analysis.Trend = MarketTrend.Bearish;
                analysis.Strength = Math.Min(1m, (sma20 - currentPrice) / sma20 * 10);
            }
            else
            {
                analysis.Trend = MarketTrend.Neutral;
                analysis.Strength = 0.5m;
            }

            // Calculate momentum
            if (prices.Count >= 5)
            {
                var recentChange = (prices.Last() - prices[prices.Count - 5]) / prices[prices.Count - 5];
                analysis.Momentum = recentChange * 100; // As percentage
            }

            // Calculate support and resistance levels
            var highs = data.TakeLast(20).Select(d => d.High).ToList();
            var lows = data.TakeLast(20).Select(d => d.Low).ToList();
            analysis.Resistance = highs.Any() ? highs.Max() : currentPrice;
            analysis.Support = lows.Any() ? lows.Min() : currentPrice;

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error analyzing timeframe trend for {symbol} {timeframe}");
            return new TrendAnalysis { Trend = MarketTrend.Unknown, Strength = 0.5m };
        }
    }

    private decimal CalculateTrendAlignment(Dictionary<string, TrendAnalysis> timeframeTrends)
    {
        try
        {
            if (!timeframeTrends.Any()) return 0.5m;

            var bullishCount = timeframeTrends.Values.Count(t => t.Trend == MarketTrend.Bullish);
            var bearishCount = timeframeTrends.Values.Count(t => t.Trend == MarketTrend.Bearish);
            var totalCount = timeframeTrends.Count;

            // Calculate alignment score (higher when trends agree)
            var maxAlignment = Math.Max(bullishCount, bearishCount);
            return (decimal)maxAlignment / totalCount;
        }
        catch
        {
            return 0.5m;
        }
    }

    private string GetDominantTimeframe(Dictionary<string, TrendAnalysis> timeframeTrends)
    {
        try
        {
            if (!timeframeTrends.Any()) return "Unknown";

            // Find timeframe with strongest trend
            var dominantTimeframe = timeframeTrends
                .OrderByDescending(kvp => kvp.Value.Strength)
                .First();

            return dominantTimeframe.Key;
        }
        catch
        {
            return "Unknown";
        }
    }

    private decimal CalculateSignalStrength(Dictionary<string, TrendAnalysis> timeframeTrends, decimal trendAlignment)
    {
        try
        {
            if (!timeframeTrends.Any()) return 0.5m;

            var avgStrength = timeframeTrends.Values.Average(t => t.Strength);
            var avgMomentum = Math.Abs(timeframeTrends.Values.Average(t => t.Momentum));

            // Combine alignment, strength, and momentum
            return (trendAlignment * 0.4m) + (avgStrength * 0.4m) + (Math.Min(1m, avgMomentum / 5) * 0.2m);
        }
        catch
        {
            return 0.5m;
        }
    }

    private async Task<decimal> CalculateVixTrend()
    {
        try
        {
            var currentVix = await GetVixAsync();
            // In a real implementation, you'd compare with historical VIX values
            // For now, return a simple trend based on current level
            return currentVix > 25 ? 0.8m : currentVix < 15 ? -0.8m : 0m;
        }
        catch
        {
            return 0m;
        }
    }

    public async Task<VolatilitySpikeAlert> CheckVolatilitySpikeAsync(string symbol)
    {
        try
        {
            _logger.LogInformation($"Checking volatility spike for {symbol}");

            var alert = new VolatilitySpikeAlert
            {
                Symbol = symbol,
                Timestamp = DateTime.UtcNow
            };

            // Get current and normal volatility
            alert.CurrentVolatility = await CalculateHistoricalVolatilityAsync(symbol, 5); // 5-day current
            alert.NormalVolatility = await CalculateHistoricalVolatilityAsync(symbol, 30); // 30-day normal

            // Calculate spike intensity
            alert.SpikeIntensity = alert.NormalVolatility > 0 ? alert.CurrentVolatility / alert.NormalVolatility : 1;

            // Determine if spike is detected
            var spikeThreshold = _configuration.GetValue<decimal>("MarketRegime:VolatilitySpikeThreshold", 1.5m);
            alert.IsSpikeDetected = alert.SpikeIntensity > spikeThreshold;

            if (alert.IsSpikeDetected)
            {
                // Determine spike type
                alert.SpikeType = await DetermineVolatilitySpikeType(symbol);
                alert.AlertMessage = $"Volatility spike detected for {symbol}: {alert.SpikeIntensity:F2}x normal levels ({alert.SpikeType})";
            }

            _logger.LogInformation($"Volatility spike check for {symbol}: {(alert.IsSpikeDetected ? "DETECTED" : "None")} (Intensity: {alert.SpikeIntensity:F2}x)");

            return alert;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error checking volatility spike for {symbol}");
            return new VolatilitySpikeAlert { Symbol = symbol, Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<List<UnusualOptionsActivity>> GetUnusualOptionsActivityAsync(List<string> symbols)
    {
        try
        {
            _logger.LogInformation($"Scanning for unusual options activity across {symbols.Count} symbols");

            var unusualActivities = new List<UnusualOptionsActivity>();

            foreach (var symbol in symbols)
            {
                try
                {
                    var activities = await ScanUnusualActivityForSymbol(symbol);
                    unusualActivities.AddRange(activities);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"Error scanning unusual activity for {symbol}");
                }
            }

            // Sort by unusuality score
            unusualActivities = unusualActivities.OrderByDescending(a => a.UnusualityScore).ToList();

            _logger.LogInformation($"Found {unusualActivities.Count} unusual options activities");

            return unusualActivities;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scanning for unusual options activity");
            return new List<UnusualOptionsActivity>();
        }
    }

    // Helper methods for enhanced market analysis
    private async Task<(decimal CallVolume, decimal PutVolume, decimal CallOpenInterest, decimal PutOpenInterest)> GetOptionsVolumeDataAsync(string symbol)
    {
        try
        {
            // Simulate options volume data - in real implementation, get from Alpaca or other data source
            var random = new Random();
            var baseVolume = random.Next(1000, 10000);

            return (
                CallVolume: baseVolume * (decimal)(0.4 + random.NextDouble() * 0.4), // 40-80% calls
                PutVolume: baseVolume * (decimal)(0.2 + random.NextDouble() * 0.4), // 20-60% puts
                CallOpenInterest: baseVolume * (decimal)(2 + random.NextDouble() * 3), // 2-5x volume
                PutOpenInterest: baseVolume * (decimal)(1.5 + random.NextDouble() * 2.5) // 1.5-4x volume
            );
        }
        catch
        {
            return (1000, 800, 5000, 4000); // Default values
        }
    }

    private async Task<decimal> CalculatePutCallRatioMA(string symbol, int periods)
    {
        try
        {
            // Simulate historical put/call ratio calculation
            var random = new Random();
            return (decimal)(0.6 + random.NextDouble() * 0.8); // 0.6 to 1.4 range
        }
        catch
        {
            return 1.0m; // Default neutral ratio
        }
    }

    private decimal CalculateVixSentiment(decimal vix)
    {
        // Convert VIX to sentiment score (-1 to 1)
        if (vix < 15) return 0.8m; // Very bullish
        if (vix < 20) return 0.4m; // Bullish
        if (vix < 25) return 0m;   // Neutral
        if (vix < 30) return -0.4m; // Bearish
        return -0.8m; // Very bearish
    }

    private decimal CalculateBreadthSentiment(MarketBreadthIndicators breadth)
    {
        var scores = new List<decimal>();

        // Advance/Decline sentiment
        if (breadth.AdvanceDeclineRatio > 1.2m) scores.Add(0.8m);
        else if (breadth.AdvanceDeclineRatio > 1.0m) scores.Add(0.4m);
        else if (breadth.AdvanceDeclineRatio > 0.8m) scores.Add(-0.4m);
        else scores.Add(-0.8m);

        // McClellan Oscillator sentiment
        if (breadth.McClellanOscillator > 50) scores.Add(0.6m);
        else if (breadth.McClellanOscillator > 0) scores.Add(0.2m);
        else if (breadth.McClellanOscillator > -50) scores.Add(-0.2m);
        else scores.Add(-0.6m);

        return scores.Average();
    }

    private decimal CalculateCompositeSentiment(MarketSentimentAnalysis sentiment)
    {
        var weights = new Dictionary<string, decimal>
        {
            ["PutCallRatio"] = 0.25m,
            ["VixSentiment"] = 0.30m,
            ["OptionsFlow"] = 0.25m,
            ["MarketBreadth"] = 0.20m
        };

        var weightedScore =
            (sentiment.PutCallRatio > 1.2m ? -20 : sentiment.PutCallRatio < 0.8m ? 20 : 0) * weights["PutCallRatio"] +
            sentiment.VixSentiment * 100 * weights["VixSentiment"] +
            sentiment.OptionsFlowSentiment * 100 * weights["OptionsFlow"] +
            sentiment.MarketBreadthSentiment * 100 * weights["MarketBreadth"];

        return Math.Max(-100, Math.Min(100, weightedScore));
    }

    private SentimentRegime DetermineSentimentRegime(decimal compositeScore)
    {
        if (compositeScore < -60) return SentimentRegime.ExtremeFear;
        if (compositeScore < -20) return SentimentRegime.Fear;
        if (compositeScore < 20) return SentimentRegime.Neutral;
        if (compositeScore < 60) return SentimentRegime.Greed;
        return SentimentRegime.ExtremeGreed;
    }

    private void AddSentimentFactors(MarketSentimentAnalysis sentiment)
    {
        if (sentiment.PutCallRatio > 1.5m)
            sentiment.SentimentFactors.Add("High put/call ratio indicates fear");
        if (sentiment.PutCallRatio < 0.5m)
            sentiment.SentimentFactors.Add("Low put/call ratio indicates complacency");
        if (sentiment.VixSentiment < -0.5m)
            sentiment.SentimentFactors.Add("High VIX indicates market stress");
        if (sentiment.VixSentiment > 0.5m)
            sentiment.SentimentFactors.Add("Low VIX indicates market complacency");
    }

    // Additional helper methods for enhanced analysis
    private async Task<decimal> CalculateUnusualActivityScore(string symbol, (decimal CallVolume, decimal PutVolume, decimal CallOpenInterest, decimal PutOpenInterest) volumeData)
    {
        try
        {
            // Calculate based on volume vs open interest ratios and historical averages
            var totalVolume = volumeData.CallVolume + volumeData.PutVolume;
            var totalOI = volumeData.CallOpenInterest + volumeData.PutOpenInterest;
            var volumeToOIRatio = totalOI > 0 ? totalVolume / totalOI : 0;

            // Score based on volume/OI ratio (higher = more unusual)
            var score = Math.Min(100, volumeToOIRatio * 50);
            return score;
        }
        catch
        {
            return 0m;
        }
    }

    private async Task<decimal> CalculateInstitutionalFlowScore(string symbol, (decimal CallVolume, decimal PutVolume, decimal CallOpenInterest, decimal PutOpenInterest) volumeData)
    {
        try
        {
            // Estimate institutional flow based on large block activity and call/put ratios
            var callPutRatio = volumeData.PutVolume > 0 ? volumeData.CallVolume / volumeData.PutVolume : 1;

            // Institutional flow tends to be more balanced, extreme ratios suggest retail activity
            var institutionalScore = callPutRatio > 0.5m && callPutRatio < 2.0m ? 50 :
                                   callPutRatio > 2.0m ? 75 : 25; // High call ratio = bullish institutional

            return institutionalScore - 50; // Convert to -50 to +50 range
        }
        catch
        {
            return 0m;
        }
    }

    private async Task<decimal> EstimateDarkPoolFlow(string symbol)
    {
        try
        {
            // Simulate dark pool flow estimation
            var random = new Random();
            return (decimal)(random.NextDouble() * 2 - 1); // -1 to 1 range
        }
        catch
        {
            return 0m;
        }
    }

    private async Task<decimal> CalculateOptionsFlowMomentum(string symbol)
    {
        try
        {
            // Calculate momentum based on recent flow changes
            var random = new Random();
            return (decimal)(random.NextDouble() * 200 - 100); // -100 to 100 range
        }
        catch
        {
            return 0m;
        }
    }

    private async Task<decimal> CalculateGarchVolatility(string symbol)
    {
        try
        {
            // Simplified GARCH(1,1) calculation
            var alpha = _configuration.GetValue<decimal>("MarketRegime:GarchAlpha", 0.1m);
            var beta = _configuration.GetValue<decimal>("MarketRegime:GarchBeta", 0.85m);
            var omega = _configuration.GetValue<decimal>("MarketRegime:GarchOmega", 0.00001m);

            var currentVol = await CalculateHistoricalVolatilityAsync(symbol, 20);
            var previousVol = await CalculateHistoricalVolatilityAsync(symbol, 21); // Previous period

            // GARCH formula: σ²(t) = ω + α*ε²(t-1) + β*σ²(t-1)
            var garchVariance = omega + alpha * (currentVol * currentVol) + beta * (previousVol * previousVol);
            return (decimal)Math.Sqrt((double)garchVariance);
        }
        catch
        {
            return await CalculateHistoricalVolatilityAsync(symbol, 20); // Fallback to historical vol
        }
    }

    private async Task<decimal> CalculateVolatilityClusteringScore(string symbol)
    {
        try
        {
            // Calculate volatility clustering using autocorrelation of squared returns
            var returns = await GetHistoricalReturns(symbol, 60); // 60 days of returns
            if (returns.Count < 20) return 0m;

            var squaredReturns = returns.Select(r => r * r).ToList();
            var autocorr = CalculateAutocorrelation(squaredReturns, 1); // Lag-1 autocorrelation

            return Math.Max(0, Math.Min(100, autocorr * 100)); // Scale to 0-100
        }
        catch
        {
            return 50m; // Default moderate clustering
        }
    }

    private async Task<decimal> CalculateVolatilityBreakoutProbability(string symbol)
    {
        try
        {
            var currentVol = await CalculateHistoricalVolatilityAsync(symbol, 10);
            var averageVol = await CalculateHistoricalVolatilityAsync(symbol, 60);
            var volRatio = averageVol > 0 ? currentVol / averageVol : 1;

            // Higher probability if volatility is compressed (low relative to average)
            var probability = volRatio < 0.7m ? 80m : volRatio < 0.9m ? 60m : volRatio > 1.3m ? 20m : 40m;
            return probability;
        }
        catch
        {
            return 50m; // Default moderate probability
        }
    }

    private VolatilityRegime GetMostLikelyNextRegime(RegimeTransitionProbability transition)
    {
        if (!transition.TransitionProbabilities.Any())
            return transition.CurrentRegime;

        return transition.TransitionProbabilities.OrderByDescending(kvp => kvp.Value).First().Key;
    }

    private async Task<List<VolatilityRegimeSignal>> GenerateVolatilityRegimeSignals(string symbol, VolatilityRegimeDetection detection)
    {
        var signals = new List<VolatilityRegimeSignal>();

        // GARCH signal
        if (detection.GarchVolatility > detection.VolatilityMetrics.GetValueOrDefault("CurrentVol", 0) * 1.2m)
        {
            signals.Add(new VolatilityRegimeSignal
            {
                SignalType = "GARCH_EXPANSION",
                Strength = 0.8m,
                Description = "GARCH model indicates volatility expansion"
            });
        }

        // Clustering signal
        if (detection.VolatilityClusteringScore > 70)
        {
            signals.Add(new VolatilityRegimeSignal
            {
                SignalType = "HIGH_CLUSTERING",
                Strength = detection.VolatilityClusteringScore / 100m,
                Description = "High volatility clustering detected"
            });
        }

        // Breakout signal
        if (detection.VolatilityBreakoutProbability > 70)
        {
            signals.Add(new VolatilityRegimeSignal
            {
                SignalType = "BREAKOUT_IMMINENT",
                Strength = detection.VolatilityBreakoutProbability / 100m,
                Description = "Volatility breakout probability is high"
            });
        }

        return signals;
    }

    private async Task<decimal> GetMarketStressLevel()
    {
        try
        {
            var vix = await GetVixAsync();
            // Simple stress calculation based on VIX level
            if (vix > 30) return 0.8m;      // High stress
            if (vix > 25) return 0.6m;      // Medium stress
            if (vix > 20) return 0.4m;      // Low stress
            return 0.2m;                    // Very low stress
        }
        catch
        {
            return 0.5m;
        }
    }

    private async Task<decimal> CalculateVolatilityMomentum()
    {
        try
        {
            // Calculate momentum in volatility changes
            // In a real implementation, you'd use historical volatility data
            var currentVol = await GetVixAsync();
            // Simplified momentum calculation
            return currentVol > 25 ? 0.7m : currentVol < 15 ? -0.7m : 0m;
        }
        catch
        {
            return 0m;
        }
    }

    // Placeholder implementations for missing methods
    public async Task<MarketStressIndicators> GetMarketStressIndicatorsAsync()
    {
        try
        {
            var indicators = new MarketStressIndicators
            {
                Timestamp = DateTime.UtcNow,
                VixLevel = await GetVixAsync(),
                StressLevel = await GetMarketStressLevel() * 100
            };

            // Add stress factors based on VIX level
            if (indicators.VixLevel > 30)
                indicators.StressFactors.Add("High volatility environment");
            if (indicators.VixLevel > 25)
                indicators.StressFactors.Add("Elevated market uncertainty");

            return indicators;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating market stress indicators");
            return new MarketStressIndicators { Timestamp = DateTime.UtcNow };
        }
    }

    // Phase 1 Enhanced Market Analysis Implementation
    public async Task<MarketSentimentAnalysis> GetMarketSentimentAsync(string symbol)
    {
        try
        {
            _logger.LogInformation($"Analyzing market sentiment for {symbol}");

            var sentiment = new MarketSentimentAnalysis
            {
                Symbol = symbol,
                Timestamp = DateTime.UtcNow
            };

            // Calculate Put/Call Ratio
            var optionsData = await GetOptionsVolumeDataAsync(symbol);
            sentiment.PutCallRatio = optionsData.PutVolume > 0 ? optionsData.CallVolume / optionsData.PutVolume : 0;
            sentiment.PutCallRatioMA = await CalculatePutCallRatioMA(symbol, 20); // 20-day MA

            // VIX-based sentiment
            var vix = await GetVixAsync();
            sentiment.VixSentiment = CalculateVixSentiment(vix);

            // Options flow sentiment
            var optionsFlow = await GetOptionsFlowAnalysisAsync(symbol);
            sentiment.OptionsFlowSentiment = optionsFlow.InstitutionalFlowScore / 100m;

            // Market breadth sentiment
            var breadth = await GetMarketBreadthAsync();
            sentiment.MarketBreadthSentiment = CalculateBreadthSentiment(breadth);

            // Composite sentiment score
            sentiment.CompositeSentimentScore = CalculateCompositeSentiment(sentiment);
            sentiment.SentimentRegime = DetermineSentimentRegime(sentiment.CompositeSentimentScore);

            // Add sentiment factors
            AddSentimentFactors(sentiment);

            // Store component scores
            sentiment.SentimentComponents["PutCallRatio"] = sentiment.PutCallRatio;
            sentiment.SentimentComponents["VixSentiment"] = sentiment.VixSentiment;
            sentiment.SentimentComponents["OptionsFlow"] = sentiment.OptionsFlowSentiment;
            sentiment.SentimentComponents["MarketBreadth"] = sentiment.MarketBreadthSentiment;

            _logger.LogInformation($"Market sentiment for {symbol}: {sentiment.SentimentRegime} (Score: {sentiment.CompositeSentimentScore:F1})");

            return sentiment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error analyzing market sentiment for {symbol}");
            return new MarketSentimentAnalysis { Symbol = symbol, Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<OptionsFlowAnalysis> GetOptionsFlowAnalysisAsync(string symbol)
    {
        try
        {
            _logger.LogInformation($"Analyzing options flow for {symbol}");

            var flow = new OptionsFlowAnalysis
            {
                Symbol = symbol,
                Timestamp = DateTime.UtcNow
            };

            // Get options volume data
            var volumeData = await GetOptionsVolumeDataAsync(symbol);
            flow.CallVolume = volumeData.CallVolume;
            flow.PutVolume = volumeData.PutVolume;
            flow.CallOpenInterest = volumeData.CallOpenInterest;
            flow.PutOpenInterest = volumeData.PutOpenInterest;

            // Calculate unusual activity score
            flow.UnusualActivityScore = await CalculateUnusualActivityScore(symbol, volumeData);

            // Calculate institutional flow score
            flow.InstitutionalFlowScore = await CalculateInstitutionalFlowScore(symbol, volumeData);

            // Estimate dark pool flow
            flow.DarkPoolFlow = await EstimateDarkPoolFlow(symbol);

            // Calculate options flow momentum
            flow.OptionsFlowMomentum = await CalculateOptionsFlowMomentum(symbol);

            // Get unusual options activity
            flow.UnusualActivity = await GetUnusualOptionsActivityAsync(new List<string> { symbol });

            // Store flow metrics
            flow.FlowMetrics["CallPutVolumeRatio"] = flow.PutVolume > 0 ? flow.CallVolume / flow.PutVolume : 0;
            flow.FlowMetrics["CallPutOIRatio"] = flow.PutOpenInterest > 0 ? flow.CallOpenInterest / flow.PutOpenInterest : 0;
            flow.FlowMetrics["VolumeToOIRatio"] = flow.CallOpenInterest + flow.PutOpenInterest > 0 ?
                (flow.CallVolume + flow.PutVolume) / (flow.CallOpenInterest + flow.PutOpenInterest) : 0;

            _logger.LogInformation($"Options flow analysis for {symbol}: Unusual Activity={flow.UnusualActivityScore:F1}, Institutional={flow.InstitutionalFlowScore:F1}");

            return flow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error analyzing options flow for {symbol}");
            return new OptionsFlowAnalysis { Symbol = symbol, Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<VolatilityRegimeDetection> GetEnhancedVolatilityRegimeAsync(string symbol)
    {
        try
        {
            _logger.LogInformation($"Performing enhanced volatility regime detection for {symbol}");

            var detection = new VolatilityRegimeDetection
            {
                Symbol = symbol,
                Timestamp = DateTime.UtcNow
            };

            // Get current regime
            var currentRegime = await GetCurrentRegimeAsync();
            detection.CurrentRegime = currentRegime.VolatilityRegime;

            // Calculate GARCH volatility
            detection.GarchVolatility = await CalculateGarchVolatility(symbol);

            // Calculate volatility clustering score
            detection.VolatilityClusteringScore = await CalculateVolatilityClusteringScore(symbol);

            // Predict next regime
            var regimeTransition = await GetRegimeTransitionProbabilityAsync();
            detection.PredictedRegime = GetMostLikelyNextRegime(regimeTransition);
            detection.RegimeConfidence = regimeTransition.RegimeStability;

            // Calculate volatility breakout probability
            detection.VolatilityBreakoutProbability = await CalculateVolatilityBreakoutProbability(symbol);

            // Generate regime signals
            detection.RegimeSignals = await GenerateVolatilityRegimeSignals(symbol, detection);

            // Store volatility metrics
            detection.VolatilityMetrics["CurrentVol"] = await CalculateHistoricalVolatilityAsync(symbol, 20);
            detection.VolatilityMetrics["GarchVol"] = detection.GarchVolatility;
            detection.VolatilityMetrics["ClusteringScore"] = detection.VolatilityClusteringScore;
            detection.VolatilityMetrics["BreakoutProb"] = detection.VolatilityBreakoutProbability;

            _logger.LogInformation($"Enhanced volatility regime for {symbol}: {detection.CurrentRegime} -> {detection.PredictedRegime} (Confidence: {detection.RegimeConfidence:P1})");

            return detection;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error in enhanced volatility regime detection for {symbol}");
            return new VolatilityRegimeDetection { Symbol = symbol, Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<MarketBreadthIndicators> GetMarketBreadthAsync()
    {
        try
        {
            _logger.LogInformation("Calculating market breadth indicators");

            var breadth = new MarketBreadthIndicators
            {
                Timestamp = DateTime.UtcNow
            };

            // Calculate advance/decline ratio (simulated for major indices)
            breadth.AdvanceDeclineRatio = await CalculateAdvanceDeclineRatio();

            // Calculate new highs/new lows
            breadth.NewHighsNewLows = await CalculateNewHighsNewLows();

            // Calculate up volume/down volume
            breadth.UpVolumeDownVolume = await CalculateUpDownVolumeRatio();

            // Calculate McClellan Oscillator
            breadth.McClellanOscillator = await CalculateMcClellanOscillator();

            // Calculate breadth thrust
            breadth.BreadthThrust = await CalculateBreadthThrust();

            // Calculate sector rotation score
            breadth.SectorRotationScore = await CalculateSectorRotationScore();

            // Determine breadth regime
            breadth.BreadthRegime = DetermineBreadthRegime(breadth);

            // Generate breadth signals
            breadth.BreadthSignals = GenerateBreadthSignals(breadth);

            _logger.LogInformation($"Market breadth: {breadth.BreadthRegime} (A/D: {breadth.AdvanceDeclineRatio:F2}, McClellan: {breadth.McClellanOscillator:F1})");

            return breadth;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating market breadth indicators");
            return new MarketBreadthIndicators { Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<CorrelationBreakdownAlert> CheckCorrelationBreakdownAsync(List<string> symbols)
    {
        try
        {
            _logger.LogInformation($"Checking correlation breakdown for {symbols.Count} symbols");

            var alert = new CorrelationBreakdownAlert
            {
                Timestamp = DateTime.UtcNow,
                Symbols = symbols
            };

            var correlationPairs = new List<CorrelationPair>();

            // Calculate correlations between all symbol pairs
            for (int i = 0; i < symbols.Count; i++)
            {
                for (int j = i + 1; j < symbols.Count; j++)
                {
                    var pair = await CalculateCorrelationPair(symbols[i], symbols[j]);
                    correlationPairs.Add(pair);
                }
            }

            alert.BreakdownPairs = correlationPairs;

            // Check for significant correlation changes
            var significantChanges = correlationPairs.Where(p => Math.Abs(p.CorrelationChange) > 0.3m).ToList();
            alert.IsBreakdownDetected = significantChanges.Any();

            if (alert.IsBreakdownDetected)
            {
                alert.CorrelationChange = significantChanges.Max(p => Math.Abs(p.CorrelationChange));
                alert.AlertSeverity = Math.Min(100, alert.CorrelationChange * 200); // Scale to 0-100
                alert.AlertMessage = $"Correlation breakdown detected: {significantChanges.Count} pairs with significant changes";
            }

            _logger.LogInformation($"Correlation breakdown check: {(alert.IsBreakdownDetected ? "DETECTED" : "None")} (Severity: {alert.AlertSeverity:F1})");

            return alert;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking correlation breakdown");
            return new CorrelationBreakdownAlert { Timestamp = DateTime.UtcNow, Symbols = symbols };
        }
    }

    public async Task<VolatilitySurface> GetVolatilitySurfaceAsync(string symbol)
    {
        try
        {
            var surface = new VolatilitySurface
            {
                Symbol = symbol,
                Timestamp = DateTime.UtcNow,
                AtmVolatility = await GetVixAsync()
            };

            // Simplified volatility surface - in reality, this would use option chain data
            surface.VolatilitySkew = 0.05m; // Typical put skew
            surface.TermStructureSlope = 0.02m; // Typical term structure

            return surface;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating volatility surface for {symbol}");
            return new VolatilitySurface { Symbol = symbol, Timestamp = DateTime.UtcNow };
        }
    }

    // Helper methods for VIX-equivalent calculations
    private async Task<decimal> CalculateIntradayVolatilityAsync(string symbol)
    {
        try
        {
            // Get today's intraday data (if available)
            var today = DateTime.Today;
            var historicalData = await _alpacaService.GetHistoricalDataAsync(symbol, today, DateTime.UtcNow);

            if (historicalData == null || historicalData.Count < 2)
            {
                _logger.LogDebug($"Insufficient intraday data for {symbol}");
                return 0;
            }

            // Calculate intraday returns
            var returns = new List<decimal>();
            for (int i = 1; i < historicalData.Count; i++)
            {
                var previousClose = historicalData[i - 1].Close;
                var currentClose = historicalData[i].Close;

                if (previousClose > 0)
                {
                    var return_ = (decimal)Math.Log((double)(currentClose / previousClose));
                    returns.Add(return_);
                }
            }

            if (returns.Count < 2)
            {
                _logger.LogDebug($"Insufficient intraday returns for {symbol}");
                return 0;
            }

            // Calculate standard deviation of intraday returns
            var mean = returns.Average();
            var variance = returns.Select(r => (r - mean) * (r - mean)).Average();
            var stdDev = (decimal)Math.Sqrt((double)variance);

            // Annualize assuming 6.5 trading hours per day, 252 trading days
            var intradayPeriods = returns.Count;
            var periodsPerDay = Math.Max(1, intradayPeriods); // Avoid division by zero
            var annualizedVol = stdDev * (decimal)Math.Sqrt(periodsPerDay * 252) * 100;

            _logger.LogDebug($"Intraday volatility for {symbol}: {annualizedVol:F2}%");
            return annualizedVol;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating intraday volatility for {symbol}");
            return 0;
        }
    }

    private async Task<decimal> CalculateHistoricalVolatilityAsync(string symbol, int periods)
    {
        try
        {
            // Get historical price data with extended buffer to ensure sufficient data
            var endDate = DateTime.UtcNow;
            var bufferDays = Math.Max(30, periods * 2); // Ensure adequate buffer
            var startDate = endDate.AddDays(-(periods + bufferDays));

            var historicalData = await _alpacaService.GetHistoricalDataAsync(symbol, startDate, endDate);

            if (historicalData == null || historicalData.Count == 0)
            {
                _logger.LogDebug($"No historical data available for {symbol}, using fallback volatility");
                return GetFallbackVolatility(symbol);
            }

            // If we still don't have enough data, use what we have but log it
            if (historicalData.Count < periods)
            {
                _logger.LogDebug($"Limited historical data for {symbol}: {historicalData.Count} bars (requested {periods})");
                periods = Math.Max(5, historicalData.Count - 1); // Use at least 5 periods if available
            }

            // Calculate daily returns
            var returns = new List<decimal>();
            for (int i = 1; i < historicalData.Count; i++)
            {
                var previousClose = historicalData[i - 1].Close;
                var currentClose = historicalData[i].Close;

                if (previousClose > 0)
                {
                    var dailyReturn = (decimal)Math.Log((double)(currentClose / previousClose));
                    returns.Add(dailyReturn);
                }
            }

            if (returns.Count < 5) // Minimum 5 returns for meaningful calculation
            {
                _logger.LogDebug($"Insufficient return data for {symbol}: {returns.Count} returns");
                return GetFallbackVolatility(symbol);
            }

            // Take the most recent 'periods' returns (or all if we have fewer)
            var recentReturns = returns.TakeLast(Math.Min(periods, returns.Count)).ToList();

            // Calculate standard deviation
            var mean = recentReturns.Average();
            var variance = recentReturns.Select(r => (r - mean) * (r - mean)).Average();
            var stdDev = (decimal)Math.Sqrt((double)variance);

            // Annualize (252 trading days) and convert to percentage
            var annualizedVol = stdDev * (decimal)Math.Sqrt(252) * 100;

            // Sanity check - ensure volatility is within reasonable bounds
            annualizedVol = Math.Max(5m, Math.Min(200m, annualizedVol));

            _logger.LogDebug($"Calculated {periods}-period volatility for {symbol}: {annualizedVol:F2}% (from {recentReturns.Count} returns)");
            return annualizedVol;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating historical volatility for {symbol}");
            return GetFallbackVolatility(symbol);
        }
    }

    private decimal GetFallbackVolatility(string symbol)
    {
        // Return reasonable default volatilities based on asset class
        return symbol switch
        {
            "SPY" or "SPX" => 18m,  // 18% annual volatility for S&P 500
            "QQQ" => 22m,           // 22% for NASDAQ
            "IWM" => 25m,           // 25% for small caps
            "VIX" => 80m,           // 80% for VIX
            _ => 20m                // 20% default
        };
    }

    private async Task<decimal> CalculateATMImpliedVolatilityAsync(string symbol)
    {
        try
        {
            // Get current price
            var currentPrice = await _alpacaService.GetCurrentPriceAsync(symbol);
            if (currentPrice <= 0)
                return 0;

            // Get 0 DTE options chain
            var optionsChain = await _alpacaService.GetOptionChainAsync(symbol, DateTime.Today);

            if (optionsChain == null || !optionsChain.Any())
            {
                _logger.LogDebug($"No options data available for {symbol}");
                return 0;
            }

            // Find ATM call and put options
            var atmCall = optionsChain
                .Where(o => o.OptionType == Models.OptionType.Call)
                .OrderBy(o => Math.Abs(o.StrikePrice - currentPrice))
                .FirstOrDefault();

            var atmPut = optionsChain
                .Where(o => o.OptionType == Models.OptionType.Put)
                .OrderBy(o => Math.Abs(o.StrikePrice - currentPrice))
                .FirstOrDefault();

            var impliedVols = new List<decimal>();

            // Calculate IV from ATM options if available
            if (atmCall != null && atmCall.ImpliedVolatility > 0)
            {
                impliedVols.Add(atmCall.ImpliedVolatility * 100); // Convert to percentage
            }

            if (atmPut != null && atmPut.ImpliedVolatility > 0)
            {
                impliedVols.Add(atmPut.ImpliedVolatility * 100); // Convert to percentage
            }

            if (impliedVols.Any())
            {
                var avgIV = impliedVols.Average();
                _logger.LogDebug($"ATM implied volatility for {symbol}: {avgIV:F2}%");
                return avgIV;
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating ATM implied volatility for {symbol}");
            return 0;
        }
    }

    private async Task<decimal> CalculateATRVolatilityAsync(string symbol, int periods)
    {
        try
        {
            // Get historical price data
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-(periods + 5)); // Extra days for weekends

            var historicalData = await _alpacaService.GetHistoricalDataAsync(symbol, startDate, endDate);

            if (historicalData == null || historicalData.Count == 0)
            {
                _logger.LogDebug($"No historical data available for ATR calculation for {symbol}");
                return GetFallbackVolatility(symbol);
            }

            // If we don't have enough data, adjust periods
            if (historicalData.Count < periods + 1)
            {
                _logger.LogDebug($"Limited data for ATR calculation for {symbol}: {historicalData.Count} bars");
                periods = Math.Max(5, historicalData.Count - 1);
            }

            // Calculate True Range for each day
            var trueRanges = new List<decimal>();
            for (int i = 1; i < historicalData.Count; i++)
            {
                var current = historicalData[i];
                var previous = historicalData[i - 1];

                var tr1 = current.High - current.Low;
                var tr2 = Math.Abs(current.High - previous.Close);
                var tr3 = Math.Abs(current.Low - previous.Close);

                var trueRange = Math.Max(tr1, Math.Max(tr2, tr3));
                trueRanges.Add(trueRange);
            }

            if (trueRanges.Count < 5) // Minimum for meaningful calculation
            {
                _logger.LogDebug($"Insufficient true range data for {symbol}: {trueRanges.Count} ranges");
                return GetFallbackVolatility(symbol);
            }

            // Calculate ATR (Average True Range)
            var recentTR = trueRanges.TakeLast(periods).ToList();
            var atr = recentTR.Average();

            // Get current price for percentage calculation
            var currentPrice = await _alpacaService.GetCurrentPriceAsync(symbol);
            if (currentPrice <= 0)
                return GetFallbackVolatility(symbol);

            // Convert ATR to volatility percentage (annualized)
            var atrVolatility = (atr / currentPrice) * (decimal)Math.Sqrt(252) * 100;

            _logger.LogDebug($"ATR volatility for {symbol}: {atrVolatility:F2}%");
            return atrVolatility;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating ATR volatility for {symbol}");
            return GetFallbackVolatility(symbol);
        }
    }

    // Missing helper methods for market breadth and correlation analysis
    private async Task<decimal> CalculateAdvanceDeclineRatio()
    {
        try
        {
            // Simulate advance/decline ratio for major market indices
            var random = new Random();
            return (decimal)(0.5 + random.NextDouble()); // 0.5 to 1.5 range
        }
        catch
        {
            return 1.0m; // Neutral
        }
    }

    private async Task<decimal> CalculateNewHighsNewLows()
    {
        try
        {
            // Simulate new highs vs new lows ratio
            var random = new Random();
            return (decimal)(random.NextDouble() * 4 - 2); // -2 to 2 range
        }
        catch
        {
            return 0m; // Neutral
        }
    }

    private async Task<decimal> CalculateUpDownVolumeRatio()
    {
        try
        {
            // Simulate up volume vs down volume ratio
            var random = new Random();
            return (decimal)(0.3 + random.NextDouble() * 1.4); // 0.3 to 1.7 range
        }
        catch
        {
            return 1.0m; // Neutral
        }
    }

    private async Task<decimal> CalculateMcClellanOscillator()
    {
        try
        {
            // Simulate McClellan Oscillator
            var random = new Random();
            return (decimal)(random.NextDouble() * 200 - 100); // -100 to 100 range
        }
        catch
        {
            return 0m; // Neutral
        }
    }

    private async Task<decimal> CalculateBreadthThrust()
    {
        try
        {
            // Simulate breadth thrust indicator
            var random = new Random();
            return (decimal)(random.NextDouble() * 100); // 0 to 100 range
        }
        catch
        {
            return 50m; // Neutral
        }
    }

    private async Task<decimal> CalculateSectorRotationScore()
    {
        try
        {
            // Simulate sector rotation score
            var random = new Random();
            return (decimal)(random.NextDouble() * 100); // 0 to 100 range
        }
        catch
        {
            return 50m; // Neutral
        }
    }

    private BreadthRegime DetermineBreadthRegime(MarketBreadthIndicators breadth)
    {
        var score = 0;

        // Score based on advance/decline ratio
        if (breadth.AdvanceDeclineRatio > 1.2m) score += 2;
        else if (breadth.AdvanceDeclineRatio > 1.0m) score += 1;
        else if (breadth.AdvanceDeclineRatio < 0.8m) score -= 2;
        else if (breadth.AdvanceDeclineRatio < 1.0m) score -= 1;

        // Score based on McClellan Oscillator
        if (breadth.McClellanOscillator > 50) score += 2;
        else if (breadth.McClellanOscillator > 0) score += 1;
        else if (breadth.McClellanOscillator < -50) score -= 2;
        else score -= 1;

        // Score based on breadth thrust
        if (breadth.BreadthThrust > 80) score += 1;
        else if (breadth.BreadthThrust < 20) score -= 1;

        return score switch
        {
            >= 4 => BreadthRegime.Expanding,
            >= 2 => BreadthRegime.Strong,
            >= 0 => BreadthRegime.Neutral,
            >= -2 => BreadthRegime.Weak,
            _ => BreadthRegime.Deteriorating
        };
    }

    private List<string> GenerateBreadthSignals(MarketBreadthIndicators breadth)
    {
        var signals = new List<string>();

        if (breadth.AdvanceDeclineRatio > 1.5m)
            signals.Add("Strong advance/decline ratio indicates broad market strength");
        if (breadth.AdvanceDeclineRatio < 0.5m)
            signals.Add("Weak advance/decline ratio indicates broad market weakness");
        if (breadth.McClellanOscillator > 75)
            signals.Add("McClellan Oscillator indicates overbought conditions");
        if (breadth.McClellanOscillator < -75)
            signals.Add("McClellan Oscillator indicates oversold conditions");
        if (breadth.BreadthThrust > 90)
            signals.Add("Breadth thrust indicates strong momentum");

        return signals;
    }

    private async Task<CorrelationPair> CalculateCorrelationPair(string symbol1, string symbol2)
    {
        try
        {
            // Simulate correlation calculation between two symbols
            var random = new Random();
            var previousCorr = (decimal)(random.NextDouble() * 2 - 1); // -1 to 1
            var currentCorr = previousCorr + (decimal)(random.NextDouble() * 0.6 - 0.3); // Add some change
            currentCorr = Math.Max(-1, Math.Min(1, currentCorr)); // Clamp to valid range

            return new CorrelationPair
            {
                Symbol1 = symbol1,
                Symbol2 = symbol2,
                PreviousCorrelation = previousCorr,
                CurrentCorrelation = currentCorr,
                CorrelationChange = currentCorr - previousCorr
            };
        }
        catch
        {
            return new CorrelationPair
            {
                Symbol1 = symbol1,
                Symbol2 = symbol2,
                PreviousCorrelation = 0.5m,
                CurrentCorrelation = 0.5m,
                CorrelationChange = 0m
            };
        }
    }

    private async Task<VolatilitySpikeType> DetermineVolatilitySpikeType(string symbol)
    {
        try
        {
            // Simulate spike type determination based on timing and characteristics
            var random = new Random();
            var spikeTypes = Enum.GetValues<VolatilitySpikeType>();
            return spikeTypes[random.Next(spikeTypes.Length)];
        }
        catch
        {
            return VolatilitySpikeType.Intraday;
        }
    }

    private async Task<List<UnusualOptionsActivity>> ScanUnusualActivityForSymbol(string symbol)
    {
        try
        {
            var activities = new List<UnusualOptionsActivity>();
            var random = new Random();

            // Generate 0-3 unusual activities per symbol
            var activityCount = random.Next(0, 4);

            for (int i = 0; i < activityCount; i++)
            {
                var activity = new UnusualOptionsActivity
                {
                    Symbol = symbol,
                    Timestamp = DateTime.UtcNow,
                    OptionType = random.Next(2) == 0 ? Models.OptionType.Call : Models.OptionType.Put,
                    StrikePrice = 400 + random.Next(200), // Random strike around current levels
                    ExpirationDate = DateTime.Today, // 0 DTE
                    Volume = random.Next(1000, 10000),
                    AverageVolume = random.Next(100, 1000),
                    UnusualityScore = random.Next(60, 100), // High scores only
                    ActivityType = (ActivityType)random.Next(Enum.GetValues<ActivityType>().Length),
                    Description = "Large block trade detected"
                };

                activity.VolumeRatio = activity.AverageVolume > 0 ? activity.Volume / activity.AverageVolume : 1;
                activities.Add(activity);
            }

            return activities;
        }
        catch
        {
            return new List<UnusualOptionsActivity>();
        }
    }

    private async Task<List<decimal>> GetHistoricalReturns(string symbol, int days)
    {
        try
        {
            var endDate = DateTime.Today;
            var startDate = endDate.AddDays(-(days + 1));
            var historicalData = await _alpacaService.GetHistoricalDataAsync(symbol, startDate, endDate);
            if (historicalData == null || historicalData.Count < 2)
                return new List<decimal>();

            var returns = new List<decimal>();
            for (int i = 1; i < historicalData.Count; i++)
            {
                var previousClose = historicalData[i - 1].Close;
                var currentClose = historicalData[i].Close;
                if (previousClose > 0)
                {
                    var dailyReturn = (currentClose - previousClose) / previousClose;
                    returns.Add(dailyReturn);
                }
            }

            return returns;
        }
        catch
        {
            return new List<decimal>();
        }
    }

    private decimal CalculateAutocorrelation(List<decimal> data, int lag)
    {
        try
        {
            if (data.Count <= lag)
                return 0m;

            var n = data.Count - lag;
            var mean = data.Average();

            var numerator = 0m;
            var denominator = 0m;

            for (int i = 0; i < n; i++)
            {
                numerator += (data[i] - mean) * (data[i + lag] - mean);
            }

            for (int i = 0; i < data.Count; i++)
            {
                denominator += (data[i] - mean) * (data[i] - mean);
            }

            return denominator != 0 ? numerator / denominator : 0m;
        }
        catch
        {
            return 0m;
        }
    }
}






