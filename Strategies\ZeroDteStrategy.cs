using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ZeroDateStrat.Utils;

namespace ZeroDateStrat.Strategies;

public interface IZeroDteStrategy
{
    Task<List<TradingSignal>> GenerateSignalsAsync();
    Task<bool> ExecuteSignalAsync(TradingSignal signal);
    Task ManagePositionsAsync();
    Task<bool> ShouldTrade();
    Task<bool> IsOptimalEntryTimeAsync();
    Task<ExitDecision> ShouldExitPositionAsync(Position position);
    Task<decimal> CalculatePortfolioHeatAsync();
}

public class ZeroDteStrategy : IZeroDteStrategy
{
    private readonly ILogger<ZeroDteStrategy> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;
    private readonly IOptionsScanner _optionsScanner;
    private readonly IRiskManager _riskManager;
    private readonly IMarketRegimeAnalyzer _marketRegimeAnalyzer;
    private readonly ITradingNotificationService _notificationService;
    private readonly IGlobalExceptionHandler _exceptionHandler;
    private readonly List<Position> _activePositions = new();

    // Focus on SPX for best 0 DTE trading (tax advantages, cash settlement, no assignment risk)
    private readonly List<string> _watchlist = new()
    {
        "SPX", "SPY" // SPX primary, SPY backup
    };

    // Portfolio heat tracking
    private decimal _currentPortfolioHeat = 0m;

    public ZeroDteStrategy(
        ILogger<ZeroDteStrategy> logger,
        IConfiguration configuration,
        IAlpacaService alpacaService,
        IOptionsScanner optionsScanner,
        IRiskManager riskManager,
        IMarketRegimeAnalyzer marketRegimeAnalyzer,
        ITradingNotificationService notificationService,
        IGlobalExceptionHandler exceptionHandler)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
        _optionsScanner = optionsScanner;
        _riskManager = riskManager;
        _marketRegimeAnalyzer = marketRegimeAnalyzer;
        _notificationService = notificationService;
        _exceptionHandler = exceptionHandler;
    }

    public async Task<bool> ShouldTrade()
    {
        try
        {
            var now = DateTime.Now;
            var forceClose = TimeSpan.Parse(_configuration["Trading:ForceCloseTime"] ?? "15:45:00");
            var tradingEnd = TimeSpan.Parse(_configuration["Trading:TradingEndTime"] ?? "16:00:00");

            var inTradingHours = now.TimeOfDay < tradingEnd;

            if (!inTradingHours)
            {
                _logger.LogInformation("Outside trading hours");
                return false;
            }

            // Use enhanced entry timing instead of fixed windows
            var optimalEntryTime = await IsOptimalEntryTimeAsync();
            var inManagementWindow = now.TimeOfDay > TimeSpan.FromHours(10.5) && now.TimeOfDay < forceClose;

            if (!optimalEntryTime && !inManagementWindow)
            {
                _logger.LogInformation("Not optimal entry time - only position management allowed");
                return false;
            }

            // Check if it's a weekday
            if (now.DayOfWeek == DayOfWeek.Saturday || now.DayOfWeek == DayOfWeek.Sunday)
            {
                _logger.LogInformation("Weekend - no trading");
                return false;
            }

            // Check account status and buying power
            var account = await _alpacaService.GetAccountAsync();
            if (account == null)
            {
                _logger.LogWarning("Could not retrieve account information");
                return false;
            }

            // Basic account validation
            if (account.Equity < 1000)
            {
                _logger.LogWarning("Insufficient account equity");
                return false;
            }

            // Check daily loss limit
            var maxDailyLoss = _configuration.GetValue<decimal>("Trading:MaxDailyLoss", 500);
            if (await _riskManager.GetDailyPnLAsync() <= -maxDailyLoss)
            {
                _logger.LogWarning($"Daily loss limit reached: {maxDailyLoss:C}");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if should trade");
            return false;
        }
    }

    public async Task<bool> IsOptimalEntryTimeAsync()
    {
        try
        {
            var now = DateTime.Now.TimeOfDay;

            // Basic time window check (9:30 AM - 11:30 AM)
            if (now < TimeSpan.FromHours(9.5) || now > TimeSpan.FromHours(11.5))
            {
                return false;
            }

            // Get market regime for volatility assessment
            var regime = await _marketRegimeAnalyzer.GetCurrentRegimeAsync();

            // Wait for market open volatility to settle (first 15-30 minutes)
            var marketOpenTime = TimeSpan.FromHours(9.5); // 9:30 AM
            var timeSinceOpen = now - marketOpenTime;

            // If VIX is high, wait longer for volatility to settle
            var minWaitTime = regime.Vix > 25 ? TimeSpan.FromMinutes(30) : TimeSpan.FromMinutes(15);

            if (timeSinceOpen < minWaitTime)
            {
                _logger.LogDebug($"Waiting for market volatility to settle. Time since open: {timeSinceOpen.TotalMinutes:F1} min, Required: {minWaitTime.TotalMinutes} min");
                return false;
            }

            // Check if volatility is too high for safe entry
            if (regime.Vix > 30)
            {
                _logger.LogInformation($"VIX too high for safe entry: {regime.Vix:F2}");
                return false;
            }

            // Check volume and liquidity (simplified check)
            var hasLiquidity = await HasSufficientLiquidityAsync();
            if (!hasLiquidity)
            {
                _logger.LogDebug("Insufficient market liquidity for optimal entry");
                return false;
            }

            _logger.LogInformation($"Optimal entry conditions met. VIX: {regime.Vix:F2}, Time: {now:hh\\:mm}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking optimal entry time");
            return false;
        }
    }

    private async Task<bool> HasSufficientLiquidityAsync()
    {
        try
        {
            // Simplified liquidity check - in production, this would check actual volume data
            // For now, assume liquidity is sufficient during normal trading hours
            var now = DateTime.Now.TimeOfDay;
            return now >= TimeSpan.FromHours(9.5) && now <= TimeSpan.FromHours(15.5);
        }
        catch
        {
            return true; // Default to allowing trades if check fails
        }
    }

    public async Task<List<TradingSignal>> GenerateSignalsAsync()
    {
        try
        {
            if (!await ShouldTrade())
                return new List<TradingSignal>();

            _logger.LogInformation("Scanning for 0 DTE trading opportunities...");

            // Scan for 0 DTE options
            var optionChains = await _optionsScanner.ScanForZeroDteOptionsAsync(_watchlist);
            
            if (!optionChains.Any())
            {
                _logger.LogInformation("No 0 DTE options found");
                return new List<TradingSignal>();
            }

            // Find trading opportunities
            var signals = await _optionsScanner.FindTradingOpportunitiesAsync(optionChains);

            // Apply enhanced signal quality ranking
            var rankedSignals = await _optionsScanner.RankSignalsByQualityAsync(signals, optionChains);

            // Apply risk management filters with quality validation
            var filteredSignals = new List<TradingSignal>();
            foreach (var signal in rankedSignals)
            {
                // Enhanced quality validation
                var chain = optionChains.FirstOrDefault(c => c.UnderlyingSymbol == signal.UnderlyingSymbol);
                if (chain != null)
                {
                    var qualityValidation = await _optionsScanner.ValidateSignalQualityAsync(signal, chain);
                    if (qualityValidation.IsValid && await _riskManager.ValidateSignalAsync(signal))
                    {
                        filteredSignals.Add(signal);
                        _logger.LogInformation($"Signal {signal.Strategy} for {signal.UnderlyingSymbol} passed quality validation (Score: {qualityValidation.QualityScore:F3})");
                    }
                    else
                    {
                        _logger.LogDebug($"Signal {signal.Strategy} for {signal.UnderlyingSymbol} failed validation. Quality: {qualityValidation.QualityScore:F3}, Issues: {string.Join(", ", qualityValidation.Issues)}");
                    }
                }
                else if (await _riskManager.ValidateSignalAsync(signal))
                {
                    filteredSignals.Add(signal);
                }
            }

            _logger.LogInformation($"Generated {filteredSignals.Count} high-quality signals after enhanced filtering");
            return filteredSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating trading signals");

            // Send Discord notification for trading error
            await _exceptionHandler.HandleTradingExceptionAsync(ex, "Signal Generation");

            return new List<TradingSignal>();
        }
    }

    public async Task<bool> ExecuteSignalAsync(TradingSignal signal)
    {
        try
        {
            _logger.LogInformation($"Executing signal: {signal.Strategy} for {signal.UnderlyingSymbol}");

            // Final risk check before execution
            if (!await _riskManager.ValidateSignalAsync(signal))
            {
                _logger.LogWarning($"Signal failed final risk validation: {signal.Id}");
                return false;
            }

            // Calculate position size with portfolio heat consideration
            var portfolioHeat = await CalculatePortfolioHeatAsync();
            var maxHeat = _configuration.GetValue<decimal>("Trading:MaxPortfolioHeat", 0.05m); // 5% max heat

            if (portfolioHeat >= maxHeat)
            {
                _logger.LogWarning($"Portfolio heat limit reached: {portfolioHeat:P2} >= {maxHeat:P2}");
                return false;
            }

            var positionSize = await _riskManager.CalculatePositionSizeAsync(signal);

            // Adjust position size based on remaining heat capacity
            var heatCapacity = Math.Max(0.1m, (maxHeat - portfolioHeat) / maxHeat);
            positionSize *= heatCapacity;

            if (positionSize <= 0.1m) // Minimum viable position size
            {
                _logger.LogWarning($"Position size too small after heat adjustment: {positionSize:F2}");
                return false;
            }

            // Adjust quantities based on position size
            foreach (var leg in signal.Legs)
            {
                leg.Quantity = (int)(leg.Quantity * positionSize);
            }

            // Place the order
            var order = await _alpacaService.PlaceOrderAsync(signal);
            if (order == null)
            {
                _logger.LogError($"Failed to place order for signal: {signal.Id}");

                // Send failure notification
                await _notificationService.SendTradeExecutionNotificationAsync(signal, null, false);

                return false;
            }

            // Create position tracking
            var position = new Position
            {
                Strategy = signal.Strategy,
                UnderlyingSymbol = signal.UnderlyingSymbol,
                Legs = signal.Legs,
                OpenCredit = signal.Legs.Where(l => l.Side == OrderSide.Sell).Sum(l => l.Price * l.Quantity) -
                           signal.Legs.Where(l => l.Side == OrderSide.Buy).Sum(l => l.Price * l.Quantity),
                OpenedAt = DateTime.UtcNow,
                ExpirationDate = signal.ExpirationDate,
                ProfitTarget = _configuration.GetValue<decimal>($"Strategies:{signal.Strategy}:ProfitTarget", 0.5m),
                StopLoss = _configuration.GetValue<decimal>($"Strategies:{signal.Strategy}:StopLoss", 0.8m)
            };

            _activePositions.Add(position);

            signal.IsExecuted = true;
            signal.ExecutedAt = DateTime.UtcNow;

            // Send success notification
            await _notificationService.SendTradeExecutionNotificationAsync(signal, order, true);

            _logger.LogInformation($"Successfully executed signal: {signal.Id}, Order ID: {order.OrderId}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error executing signal: {signal.Id}");

            // Send Discord notification for trading error
            await _exceptionHandler.HandleTradingExceptionAsync(ex, $"Signal Execution - {signal.Strategy} for {signal.UnderlyingSymbol}");

            return false;
        }
    }

    public async Task ManagePositionsAsync()
    {
        try
        {
            _logger.LogInformation($"Managing {_activePositions.Count} active positions");

            var positionsToClose = new List<Position>();

            foreach (var position in _activePositions.Where(p => p.Status == PositionStatus.Open))
            {
                // Update position value and P&L
                await UpdatePositionValue(position);

                // Use enhanced exit decision logic
                var exitDecision = await ShouldExitPositionAsync(position);
                if (exitDecision.ShouldExit)
                {
                    _logger.LogInformation($"Exit decision for position {position.Id}: {exitDecision.Reason}");
                    positionsToClose.Add(position);
                }
            }

            // Close positions that meet exit criteria
            foreach (var position in positionsToClose)
            {
                await ClosePositionAsync(position);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error managing positions");

            // Send Discord notification for trading error
            await _exceptionHandler.HandleTradingExceptionAsync(ex, "Position Management");
        }
    }

    public async Task<ExitDecision> ShouldExitPositionAsync(Position position)
    {
        try
        {
            var timeToExpiry = (position.ExpirationDate - DateTime.UtcNow).TotalHours;
            var currentPnL = position.UnrealizedPnL / Math.Max(position.OpenCredit, 1); // Avoid division by zero
            var regime = await _marketRegimeAnalyzer.GetCurrentRegimeAsync();

            // Time-based exit acceleration
            if (timeToExpiry < 1) // Last hour
            {
                // Take any profit > 25% in final hour
                if (currentPnL > 0.25m)
                    return new ExitDecision { ShouldExit = true, Reason = "Final hour profit taking (25%+)" };

                // Cut losses more aggressively in final hour
                if (currentPnL < -1.0m)
                    return new ExitDecision { ShouldExit = true, Reason = "Final hour aggressive loss cutting" };
            }
            else if (timeToExpiry < 3) // Last 3 hours
            {
                // Volatility-adjusted exits
                if (regime.Vix > 25 && currentPnL > 0.30m)
                    return new ExitDecision { ShouldExit = true, Reason = "High volatility profit taking (30%+)" };

                // Tighter stops in high volatility
                if (regime.Vix > 30 && currentPnL < -0.75m)
                    return new ExitDecision { ShouldExit = true, Reason = "High volatility tighter stop loss" };
            }

            // Market regime-based adjustments
            if (regime.VolatilityRegime == VolatilityRegime.High)
            {
                // More conservative exits in high volatility
                if (currentPnL >= 0.40m)
                    return new ExitDecision { ShouldExit = true, Reason = "High volatility regime profit target" };

                if (currentPnL <= -1.5m)
                    return new ExitDecision { ShouldExit = true, Reason = "High volatility regime stop loss" };
            }

            // Standard profit target and stop loss
            var profitTarget = _configuration.GetValue<decimal>($"Strategies:{position.Strategy}:ProfitTarget", 0.50m);
            var stopLoss = _configuration.GetValue<decimal>($"Strategies:{position.Strategy}:StopLoss", -2.0m);

            if (currentPnL >= profitTarget)
                return new ExitDecision { ShouldExit = true, Reason = $"Standard profit target ({profitTarget:P0})" };

            if (currentPnL <= stopLoss)
                return new ExitDecision { ShouldExit = true, Reason = $"Standard stop loss ({stopLoss:P0})" };

            return new ExitDecision { ShouldExit = false, Reason = "Position within acceptable parameters" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error evaluating exit decision for position {position.Id}");
            return new ExitDecision { ShouldExit = false, Reason = "Error in exit evaluation" };
        }
    }

    private async Task UpdatePositionValue(Position position)
    {
        try
        {
            decimal currentValue = 0;

            foreach (var leg in position.Legs)
            {
                // In a real implementation, you'd get current option prices
                // For now, we'll use a simplified approach
                var currentPrice = leg.Price; // Placeholder
                
                if (leg.Side == OrderSide.Sell)
                    currentValue -= currentPrice * leg.Quantity;
                else
                    currentValue += currentPrice * leg.Quantity;
            }

            position.CurrentValue = currentValue;
            position.UnrealizedPnL = position.OpenCredit + currentValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating position value for {position.Id}");
        }
    }

    private async Task<bool> ClosePositionAsync(Position position)
    {
        try
        {
            _logger.LogInformation($"Closing position: {position.Id} ({position.Strategy})");

            // In a real implementation, you'd place closing orders for each leg
            // For now, we'll mark it as closed
            position.Status = PositionStatus.Closed;
            position.ClosedAt = DateTime.UtcNow;

            var realizedPnL = position.UnrealizedPnL;
            _logger.LogInformation($"Position closed with P&L: {realizedPnL:C2}");

            // Convert Position to ManagedPosition for notification
            var managedPosition = new Models.ManagedPosition
            {
                PositionId = position.Id,
                UnderlyingSymbol = position.UnderlyingSymbol,
                Strategy = position.Strategy,
                CurrentValue = position.CurrentValue,
                UnrealizedPnL = position.UnrealizedPnL,
                OpenCredit = position.OpenCredit,
                ExpirationDate = position.ExpirationDate,
                ExitReason = "Strategy Exit"
            };

            // Send position closed notification
            await _notificationService.SendPositionUpdateAsync(managedPosition, "closed");

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error closing position: {position.Id}");
            return false;
        }
    }

    public async Task<decimal> CalculatePortfolioHeatAsync()
    {
        try
        {
            var totalHeat = 0m;
            var account = await _alpacaService.GetAccountAsync();
            var accountEquity = account?.Equity ?? 1000; // Fallback value

            foreach (var position in _activePositions.Where(p => p.Status == PositionStatus.Open))
            {
                var timeToExpiry = (position.ExpirationDate - DateTime.UtcNow).TotalHours;
                var maxLoss = Math.Abs(position.UnrealizedPnL < 0 ? position.UnrealizedPnL : position.OpenCredit * 2); // Estimate max loss

                // Heat increases as expiration approaches
                var timeMultiplier = timeToExpiry < 2 ? 2.0m :
                                   timeToExpiry < 4 ? 1.5m : 1.0m;

                var positionHeat = maxLoss * timeMultiplier;
                totalHeat += positionHeat;
            }

            // Calculate heat as percentage of account equity
            var heatPercentage = totalHeat / accountEquity;

            _currentPortfolioHeat = heatPercentage;
            _logger.LogDebug($"Portfolio heat: {heatPercentage:P2} (${totalHeat:F2} / ${accountEquity:F2})");

            return heatPercentage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating portfolio heat");
            return 0m;
        }
    }
}
