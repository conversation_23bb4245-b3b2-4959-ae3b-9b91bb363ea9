using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using System.Collections.Concurrent;

namespace ZeroDateStrat.Services;

/// <summary>
/// Synthetic VIX service that creates a VIX-equivalent index using ETF proxies
/// with z-score normalization as specified in the volatility model configuration
/// </summary>
public interface ISyntheticVixService
{
    Task<decimal> GetCurrentSyntheticVixAsync();
    Task<decimal> GetSyntheticVixChangeAsync(TimeSpan period);
    Task<SyntheticVixAnalysis> GetSyntheticVixAnalysisAsync();
    Task<bool> TestConnectionAsync();
    Task<List<SyntheticVixHistoricalData>> GetHistoricalDataAsync(int days);
}

public class SyntheticVixService : ISyntheticVixService
{
    private readonly ILogger<SyntheticVixService> _logger;
    private readonly IAlpacaService _alpacaService;
    private readonly IConfiguration _configuration;
    private readonly IPerformanceMonitoringService? _performanceMonitor;
    private readonly ISyntheticVixAnalyticsService? _analyticsService;

    // Cache for synthetic VIX data
    private decimal _cachedSyntheticVix = 0;
    private DateTime _lastSyntheticVixUpdate = DateTime.MinValue;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(1);

    // Historical data for z-score normalization (thread-safe)
    private readonly ConcurrentQueue<SyntheticVixHistoricalData> _historicalData = new();
    private readonly int _maxHistoricalPoints = 100; // Keep more data for analysis

    // Performance tracking
    private readonly ConcurrentDictionary<string, int> _componentFailures = new();
    private readonly ConcurrentDictionary<string, DateTime> _lastComponentSuccess = new();
    
    // Synthetic VIX configuration based on provided JSON specification
    private readonly SyntheticVixConfiguration _syntheticVixConfig = new()
    {
        Note = "Direct access to VIX (CBOE index) is not available.",
        SubstituteIndices = new List<SyntheticVixComponent>
        {
            new() { Symbol = "VXX", Type = "ETF", Weight = 0.5m, Source = "alpaca" },
            new() { Symbol = "UVXY", Type = "ETF", Weight = 0.3m, Source = "alpaca" },
            new() { Symbol = "SVXY", Type = "ETF", Weight = -0.2m, Source = "alpaca" }
        },
        Normalization = new SyntheticVixNormalization { Method = "z-score", Window = 20 },
        CompositeIndexLabel = "SyntheticVIX",
        Usage = "Use 'SyntheticVIX' in place of 'VIX' for volatility regime checks, signal thresholds, and entry/exit filters."
    };

    public SyntheticVixService(
        ILogger<SyntheticVixService> logger,
        IAlpacaService alpacaService,
        IConfiguration configuration,
        IPerformanceMonitoringService? performanceMonitor = null,
        ISyntheticVixAnalyticsService? analyticsService = null)
    {
        _logger = logger;
        _alpacaService = alpacaService;
        _configuration = configuration;
        _performanceMonitor = performanceMonitor;
        _analyticsService = analyticsService;

        // Initialize component tracking
        foreach (var component in _syntheticVixConfig.SubstituteIndices)
        {
            _componentFailures[component.Symbol] = 0;
            _lastComponentSuccess[component.Symbol] = DateTime.UtcNow;
        }

        _logger.LogInformation("SyntheticVixService initialized with configuration: {Config}",
            _syntheticVixConfig.CompositeIndexLabel);
    }

    public async Task<decimal> GetCurrentSyntheticVixAsync()
    {
        const string operationName = "SyntheticVIX_Calculate";
        _performanceMonitor?.StartOperation(operationName);

        try
        {
            // Check cache first
            if (_cachedSyntheticVix > 0 && DateTime.UtcNow - _lastSyntheticVixUpdate < _cacheExpiry)
            {
                _logger.LogDebug($"Returning cached {_syntheticVixConfig.CompositeIndexLabel}: {_cachedSyntheticVix:F2}");
                return _cachedSyntheticVix;
            }

            _logger.LogDebug($"Calculating {_syntheticVixConfig.CompositeIndexLabel} using Alpaca data");

            // Get component prices and calculate weighted composite
            var componentCalculations = new List<SyntheticVixCalculation>();
            var rawCompositeValue = 0m;
            var totalAbsoluteWeight = 0m;

            foreach (var component in _syntheticVixConfig.SubstituteIndices)
            {
                try
                {
                    var price = await _alpacaService.GetCurrentPriceAsync(component.Symbol);
                    if (price > 0)
                    {
                        var normalizedValue = NormalizeComponentValue(component.Symbol, price);
                        var weightedContribution = normalizedValue * component.Weight;

                        componentCalculations.Add(new SyntheticVixCalculation
                        {
                            Symbol = component.Symbol,
                            Price = price,
                            NormalizedValue = normalizedValue,
                            Weight = component.Weight,
                            Confidence = GetComponentConfidence(component.Symbol, price),
                            Timestamp = DateTime.UtcNow
                        });

                        rawCompositeValue += weightedContribution;
                        totalAbsoluteWeight += Math.Abs(component.Weight);

                        // Track successful component retrieval
                        _componentFailures[component.Symbol] = 0;
                        _lastComponentSuccess[component.Symbol] = DateTime.UtcNow;

                        _logger.LogDebug($"{_syntheticVixConfig.CompositeIndexLabel} component {component.Symbol}: " +
                                       $"${price:F2} -> normalized {normalizedValue:F2} -> weighted {weightedContribution:F2}");
                    }
                }
                catch (Exception ex)
                {
                    // Track component failures
                    _componentFailures.AddOrUpdate(component.Symbol, 1, (key, value) => value + 1);

                    // Record failure in analytics service
                    _analyticsService?.RecordComponentFailure(component.Symbol, ex);

                    _logger.LogDebug(ex, $"Failed to get price for {_syntheticVixConfig.CompositeIndexLabel} component {component.Symbol} " +
                                        $"(failure count: {_componentFailures[component.Symbol]})");
                }
            }

            if (componentCalculations.Any() && totalAbsoluteWeight > 0)
            {
                // Calculate the raw composite value
                var rawSyntheticVix = Math.Abs(rawCompositeValue);
                
                // Apply z-score normalization
                var normalizedSyntheticVix = ApplyZScoreNormalization(rawSyntheticVix);
                
                // Store historical data for future normalization
                StoreHistoricalData(rawSyntheticVix, normalizedSyntheticVix, componentCalculations);
                
                _cachedSyntheticVix = normalizedSyntheticVix;
                _lastSyntheticVixUpdate = DateTime.UtcNow;
                
                _logger.LogInformation($"Calculated {_syntheticVixConfig.CompositeIndexLabel} from {componentCalculations.Count} components: " +
                                     $"Raw={rawSyntheticVix:F2}, Normalized={normalizedSyntheticVix:F2}");
                return normalizedSyntheticVix;
            }

            // Fallback to conservative default
            _logger.LogWarning($"All {_syntheticVixConfig.CompositeIndexLabel} components failed, using conservative fallback");
            return 20.0m;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating {_syntheticVixConfig.CompositeIndexLabel}");
            return 20.0m;
        }
        finally
        {
            _performanceMonitor?.EndOperation(operationName);
        }
    }

    public async Task<decimal> GetSyntheticVixChangeAsync(TimeSpan period)
    {
        try
        {
            var currentSyntheticVix = await GetCurrentSyntheticVixAsync();
            
            // Get historical data for the specified period
            var historicalData = _historicalData.ToArray()
                .Where(h => h.Timestamp >= DateTime.UtcNow - period)
                .OrderBy(h => h.Timestamp)
                .ToList();

            if (historicalData.Any())
            {
                var oldestValue = historicalData.First().NormalizedValue;
                var change = currentSyntheticVix - oldestValue;
                
                _logger.LogDebug($"{_syntheticVixConfig.CompositeIndexLabel} change over {period}: {change:F2} " +
                               $"(from {oldestValue:F2} to {currentSyntheticVix:F2})");
                return change;
            }
            
            _logger.LogDebug($"{_syntheticVixConfig.CompositeIndexLabel} change calculation: insufficient historical data for period {period}");
            return 0m;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating {_syntheticVixConfig.CompositeIndexLabel} change for period {period}");
            return 0m;
        }
    }

    public async Task<SyntheticVixAnalysis> GetSyntheticVixAnalysisAsync()
    {
        try
        {
            var currentSyntheticVix = await GetCurrentSyntheticVixAsync();
            var zScore = CalculateCurrentZScore();
            
            var analysis = new SyntheticVixAnalysis
            {
                CurrentLevel = currentSyntheticVix,
                ZScore = zScore,
                Interpretation = GetSyntheticVixInterpretation(currentSyntheticVix),
                RiskLevel = GetRiskLevel(currentSyntheticVix),
                TradingRecommendation = GetTradingRecommendation(currentSyntheticVix),
                PositionSizeMultiplier = GetPositionSizeMultiplier(currentSyntheticVix),
                ComponentBreakdown = await GetCurrentComponentBreakdownAsync(),
                Timestamp = DateTime.UtcNow
            };

            // Record analysis in analytics service
            _analyticsService?.RecordCalculation(analysis);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error performing {_syntheticVixConfig.CompositeIndexLabel} analysis");
            return new SyntheticVixAnalysis
            {
                CurrentLevel = 20.0m,
                ZScore = 0m,
                Interpretation = "Error - Using Conservative Default",
                RiskLevel = "Medium",
                TradingRecommendation = "Proceed with caution",
                PositionSizeMultiplier = 0.8m,
                ComponentBreakdown = new List<SyntheticVixCalculation>(),
                Timestamp = DateTime.UtcNow
            };
        }
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logger.LogInformation($"Testing {_syntheticVixConfig.CompositeIndexLabel} service connection...");
            
            // Test by trying to get the primary component (VXX) price
            var vxxPrice = await _alpacaService.GetCurrentPriceAsync("VXX");
            if (vxxPrice > 0)
            {
                _logger.LogInformation($"{_syntheticVixConfig.CompositeIndexLabel} service test successful - VXX: ${vxxPrice:F2}");
                return true;
            }
            
            _logger.LogWarning($"{_syntheticVixConfig.CompositeIndexLabel} service test failed - no VXX data");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"{_syntheticVixConfig.CompositeIndexLabel} service connection test failed");
            return false;
        }
    }

    public async Task<List<SyntheticVixHistoricalData>> GetHistoricalDataAsync(int days)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-days);
            return _historicalData.ToArray()
                .Where(h => h.Timestamp >= cutoffDate)
                .OrderBy(h => h.Timestamp)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error retrieving {_syntheticVixConfig.CompositeIndexLabel} historical data");
            return new List<SyntheticVixHistoricalData>();
        }
    }

    // Private helper methods
    private decimal NormalizeComponentValue(string symbol, decimal price)
    {
        // Convert ETF prices to VIX-equivalent values
        // These conversion factors are calibrated to approximate VIX levels
        return symbol switch
        {
            "VXX" => price * 1.2m,      // VXX typically trades lower than VIX
            "UVXY" => price * 0.8m,     // UVXY is leveraged, so scale down
            "SVXY" => (50m - price) * 0.4m, // SVXY is inverse, convert appropriately
            _ => price
        };
    }

    private decimal ApplyZScoreNormalization(decimal rawValue)
    {
        var historicalValues = _historicalData.ToArray()
            .TakeLast(_syntheticVixConfig.Normalization.Window)
            .Select(h => h.RawValue)
            .ToList();

        if (historicalValues.Count < 5) // Need minimum data points
        {
            _logger.LogDebug($"Insufficient historical data for z-score normalization, using raw value");
            return Math.Max(10m, Math.Min(80m, rawValue)); // Clamp to reasonable VIX range
        }

        var mean = historicalValues.Average();
        var variance = historicalValues.Sum(v => (v - mean) * (v - mean)) / historicalValues.Count;
        var stdDev = (decimal)Math.Sqrt((double)variance);

        if (stdDev == 0) return rawValue; // Avoid division by zero

        var zScore = (rawValue - mean) / stdDev;

        // Convert z-score back to VIX-like scale (mean ~20, reasonable range 10-80)
        var normalizedValue = 20m + (zScore * 8m); // Scale z-score to VIX range

        // Clamp to reasonable VIX bounds
        normalizedValue = Math.Max(5m, Math.Min(100m, normalizedValue));

        _logger.LogDebug($"Z-score normalization: raw={rawValue:F2}, mean={mean:F2}, stdDev={stdDev:F2}, " +
                        $"zScore={zScore:F2}, normalized={normalizedValue:F2}");

        return normalizedValue;
    }

    private void StoreHistoricalData(decimal rawValue, decimal normalizedValue, List<SyntheticVixCalculation> components)
    {
        var historicalPoint = new SyntheticVixHistoricalData
        {
            Timestamp = DateTime.UtcNow,
            RawValue = rawValue,
            NormalizedValue = normalizedValue,
            ComponentValues = components.ToDictionary(c => c.Symbol, c => c.Price)
        };

        _historicalData.Enqueue(historicalPoint);

        // Maintain maximum historical points
        while (_historicalData.Count > _maxHistoricalPoints)
        {
            _historicalData.TryDequeue(out _);
        }
    }

    private decimal CalculateCurrentZScore()
    {
        var historicalValues = _historicalData.ToArray()
            .TakeLast(_syntheticVixConfig.Normalization.Window)
            .Select(h => h.NormalizedValue)
            .ToList();

        if (historicalValues.Count < 5) return 0m;

        var mean = historicalValues.Average();
        var variance = historicalValues.Sum(v => (v - mean) * (v - mean)) / historicalValues.Count;
        var stdDev = (decimal)Math.Sqrt((double)variance);

        if (stdDev == 0) return 0m;

        var currentValue = _cachedSyntheticVix > 0 ? _cachedSyntheticVix : historicalValues.LastOrDefault();
        return (currentValue - mean) / stdDev;
    }

    private decimal GetComponentConfidence(string symbol, decimal price)
    {
        // Confidence based on price reasonableness and liquidity
        return symbol switch
        {
            "VXX" when price > 5 && price < 100 => 1.0m,
            "UVXY" when price > 2 && price < 50 => 0.9m,
            "SVXY" when price > 10 && price < 200 => 0.8m,
            _ => 0.5m
        };
    }

    private async Task<List<SyntheticVixCalculation>> GetCurrentComponentBreakdownAsync()
    {
        var breakdown = new List<SyntheticVixCalculation>();

        foreach (var component in _syntheticVixConfig.SubstituteIndices)
        {
            try
            {
                var price = await _alpacaService.GetCurrentPriceAsync(component.Symbol);
                if (price > 0)
                {
                    breakdown.Add(new SyntheticVixCalculation
                    {
                        Symbol = component.Symbol,
                        Price = price,
                        NormalizedValue = NormalizeComponentValue(component.Symbol, price),
                        Weight = component.Weight,
                        Confidence = GetComponentConfidence(component.Symbol, price),
                        Timestamp = DateTime.UtcNow
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, $"Failed to get breakdown for component {component.Symbol}");
            }
        }

        return breakdown;
    }

    private string GetSyntheticVixInterpretation(decimal syntheticVix)
    {
        return syntheticVix switch
        {
            < 12 => "Very Low (Complacency Risk)",
            < 15 => "Low (Good for Premium Selling)",
            < 20 => "Normal (Standard Trading)",
            < 25 => "Elevated (Caution Advised)",
            < 30 => "High (Defensive Strategies)",
            < 35 => "Very High (Minimal Trading)",
            _ => "Extreme (Emergency Mode)"
        };
    }

    private string GetRiskLevel(decimal syntheticVix)
    {
        return syntheticVix switch
        {
            < 15 => "Low",
            < 25 => "Medium",
            < 35 => "High",
            _ => "Extreme"
        };
    }

    private string GetTradingRecommendation(decimal syntheticVix)
    {
        return syntheticVix switch
        {
            < 12 => "Aggressive premium selling, watch for volatility expansion",
            < 15 => "Good environment for credit spreads and iron condors",
            < 20 => "Standard 0 DTE strategies, normal position sizing",
            < 25 => "Reduce position sizes, focus on high probability trades",
            < 30 => "Defensive mode, minimal new positions",
            < 35 => "Emergency protocols, close risky positions",
            _ => "Market crisis mode, preserve capital"
        };
    }

    private decimal GetPositionSizeMultiplier(decimal syntheticVix)
    {
        return syntheticVix switch
        {
            < 12 => 1.5m,   // Increase size in low vol
            < 15 => 1.3m,
            < 18 => 1.1m,
            < 22 => 1.0m,   // Normal sizing
            < 25 => 0.8m,   // Reduce size
            < 30 => 0.6m,
            < 35 => 0.4m,
            _ => 0.2m       // Minimal sizing in crisis
        };
    }
}
