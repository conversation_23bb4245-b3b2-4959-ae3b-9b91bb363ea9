using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IOptionsScanner
{
    Task<List<OptionChain>> ScanForZeroDteOptionsAsync(List<string> symbols);
    Task<List<TradingSignal>> FindTradingOpportunitiesAsync(List<OptionChain> optionChains);
    Task<List<TradingSignal>> FindPutCreditSpreadOpportunitiesAsync(OptionChain chain);
    Task<List<TradingSignal>> FindCallCreditSpreadOpportunitiesAsync(OptionChain chain);
    Task<List<TradingSignal>> FindIronButterflyOpportunitiesAsync(OptionChain chain);

    // Priority 2: New Strategy Additions
    Task<List<TradingSignal>> FindIronCondorOpportunitiesAsync(OptionChain chain);
    Task<List<TradingSignal>> FindBrokenWingButterflyOpportunitiesAsync(OptionChain chain);

    // Enhanced signal quality methods
    Task<decimal> CalculateSignalQualityScoreAsync(TradingSignal signal, OptionChain chain);
    Task<List<TradingSignal>> RankSignalsByQualityAsync(List<TradingSignal> signals, List<OptionChain> chains);
    Task<SignalValidationResult> ValidateSignalQualityAsync(TradingSignal signal, OptionChain chain);

    // Enhanced pricing and Greeks methods
    Task<decimal> CalculateImpliedVolatilityAsync(OptionContract option, decimal underlyingPrice);
    Task<OptionGreeks> CalculateGreeksAsync(OptionContract option, decimal underlyingPrice, decimal riskFreeRate = 0.05m);
    Task<decimal> CalculateTheoreticalPriceAsync(OptionContract option, decimal underlyingPrice, decimal impliedVol);
    Task<bool> IsOptionFairlyPricedAsync(OptionContract option, decimal underlyingPrice);
    Task<decimal> CalculateExpectedMoveAsync(string symbol, DateTime expirationDate);
}

public class OptionsScanner : IOptionsScanner
{
    private readonly ILogger<OptionsScanner> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;
    private readonly IMarketRegimeAnalyzer _marketRegimeAnalyzer;
    private readonly ITradingCalendarService _tradingCalendar;

    public OptionsScanner(
        ILogger<OptionsScanner> logger,
        IConfiguration configuration,
        IAlpacaService alpacaService,
        IMarketRegimeAnalyzer marketRegimeAnalyzer,
        ITradingCalendarService tradingCalendar)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
        _marketRegimeAnalyzer = marketRegimeAnalyzer;
        _tradingCalendar = tradingCalendar;
    }

    public async Task<List<OptionChain>> ScanForZeroDteOptionsAsync(List<string> symbols)
    {
        var optionChains = new List<OptionChain>();
        var today = DateTime.Today;

        foreach (var symbol in symbols)
        {
            try
            {
                _logger.LogInformation($"Scanning 0 DTE options for {symbol}");

                // Check if 0 DTE options are available for this symbol today
                if (!_tradingCalendar.IsZeroDteAvailable(symbol, today))
                {
                    var nextDate = _tradingCalendar.GetNextZeroDteDate(symbol, today);
                    _logger.LogInformation($"No 0 DTE options available for {symbol} today. Next available: {nextDate?.ToString("yyyy-MM-dd") ?? "Unknown"}");
                    continue;
                }

                var currentPrice = await _alpacaService.GetCurrentPriceAsync(symbol);
                if (currentPrice <= 0)
                {
                    _logger.LogWarning($"Could not get current price for {symbol}");
                    continue;
                }

                var options = await _alpacaService.GetOptionChainAsync(symbol, today);
                if (!options.Any())
                {
                    _logger.LogInformation($"No 0 DTE options found for {symbol} (calendar says they should be available)");
                    continue;
                }

                var optionChain = new OptionChain
                {
                    UnderlyingSymbol = symbol,
                    UnderlyingPrice = currentPrice,
                    ExpirationDate = today,
                    LastUpdated = DateTime.UtcNow
                };

                // Separate calls and puts
                optionChain.Calls = options.Where(o => o.OptionType == OptionType.Call).ToList();
                optionChain.Puts = options.Where(o => o.OptionType == OptionType.Put).ToList();

                // Filter for liquid options
                optionChain.Calls = optionChain.Calls.Where(o => o.IsLiquid && o.IsZeroDte).ToList();
                optionChain.Puts = optionChain.Puts.Where(o => o.IsLiquid && o.IsZeroDte).ToList();

                if (optionChain.Calls.Any() || optionChain.Puts.Any())
                {
                    optionChains.Add(optionChain);
                    _logger.LogInformation($"Found {optionChain.Calls.Count} calls and {optionChain.Puts.Count} puts for {symbol}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error scanning options for {symbol}");
            }
        }

        return optionChains;
    }

    public async Task<List<TradingSignal>> FindTradingOpportunitiesAsync(List<OptionChain> optionChains)
    {
        var signals = new List<TradingSignal>();
        var recommendedStrategies = await _marketRegimeAnalyzer.GetRecommendedStrategiesAsync();

        if (!recommendedStrategies.Any())
        {
            _logger.LogInformation("No strategies recommended for current market regime");
            return signals;
        }

        foreach (var chain in optionChains)
        {
            try
            {
                // Only look for strategies recommended by market regime analyzer
                foreach (var strategy in recommendedStrategies)
                {
                    switch (strategy)
                    {
                        case "PutCreditSpread":
                            var putCreditSignals = await FindPutCreditSpreadOpportunitiesAsync(chain);
                            signals.AddRange(putCreditSignals);
                            break;

                        case "CallCreditSpread":
                            var callCreditSignals = await FindCallCreditSpreadOpportunitiesAsync(chain);
                            signals.AddRange(callCreditSignals);
                            break;

                        case "IronButterfly":
                            var butterflySignals = await FindIronButterflyOpportunitiesAsync(chain);
                            signals.AddRange(butterflySignals);
                            break;

                        case "IronCondor":
                            var condorSignals = await FindIronCondorOpportunitiesAsync(chain);
                            signals.AddRange(condorSignals);
                            break;

                        case "BrokenWingButterfly":
                            var brokenWingSignals = await FindBrokenWingButterflyOpportunitiesAsync(chain);
                            signals.AddRange(brokenWingSignals);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error finding opportunities for {chain.UnderlyingSymbol}");
            }
        }

        // Sort by strategy priority, then confidence
        return signals.Where(s => s.IsValid)
                     .OrderBy(s => GetStrategyPriority(s.Strategy))
                     .ThenByDescending(s => s.Confidence)
                     .ThenByDescending(s => s.RiskRewardRatio)
                     .ToList();
    }

    public async Task<List<TradingSignal>> FindPutCreditSpreadOpportunitiesAsync(OptionChain chain)
    {
        var signals = new List<TradingSignal>();

        if (!_configuration.GetValue<bool>("Strategies:PutCreditSpread:Enabled", true))
            return signals;

        try
        {
            var maxSpreadWidth = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:MaxSpreadWidth", 10);
            var minDelta = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:MinDelta", 0.05m);
            var maxDelta = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:MaxDelta", 0.15m);
            var minPremium = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:MinPremium", 0.10m);

            // Find puts with target delta range
            var suitablePuts = chain.Puts.Where(p =>
                Math.Abs(p.Delta) >= minDelta &&
                Math.Abs(p.Delta) <= maxDelta &&
                p.IsLiquid &&
                p.StrikePrice < chain.UnderlyingPrice &&
                p.MidPrice >= minPremium).ToList();

            foreach (var shortPut in suitablePuts)
            {
                var longPutStrike = shortPut.StrikePrice - maxSpreadWidth;
                var longPut = chain.GetPutByStrike(longPutStrike);

                if (longPut != null && longPut.IsLiquid)
                {
                    var credit = shortPut.MidPrice - longPut.MidPrice;
                    var maxLoss = maxSpreadWidth - credit;
                    var profitTarget = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:ProfitTarget", 0.5m);

                    if (credit >= minPremium && maxLoss > 0)
                    {
                        var signal = new TradingSignal
                        {
                            Strategy = "PutCreditSpread",
                            UnderlyingSymbol = chain.UnderlyingSymbol,
                            Type = SignalType.PutSpread,
                            ExpirationDate = chain.ExpirationDate,
                            ExpectedProfit = credit * profitTarget,
                            MaxLoss = maxLoss,
                            Confidence = CalculatePutCreditSpreadConfidence(shortPut, longPut, credit, chain),
                            Reason = $"Put Credit Spread: {shortPut.StrikePrice}/{longPut.StrikePrice}, Credit={credit:C2}, Delta={shortPut.Delta:F2}",
                            Legs = new List<OptionLeg>
                            {
                                new() { Symbol = shortPut.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Put, StrikePrice = shortPut.StrikePrice, Side = OrderSide.Sell, Quantity = 1, Price = shortPut.MidPrice, Delta = shortPut.Delta },
                                new() { Symbol = longPut.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Put, StrikePrice = longPut.StrikePrice, Side = OrderSide.Buy, Quantity = 1, Price = longPut.MidPrice, Delta = longPut.Delta }
                            }
                        };

                        signals.Add(signal);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error finding put credit spread opportunities for {chain.UnderlyingSymbol}");
        }

        return signals.Take(2).ToList(); // Limit to top 2 opportunities
    }

    public async Task<List<TradingSignal>> FindCallCreditSpreadOpportunitiesAsync(OptionChain chain)
    {
        var signals = new List<TradingSignal>();

        if (!_configuration.GetValue<bool>("Strategies:CallCreditSpread:Enabled", true))
            return signals;

        try
        {
            var maxSpreadWidth = _configuration.GetValue<decimal>("Strategies:CallCreditSpread:MaxSpreadWidth", 10);
            var minDelta = _configuration.GetValue<decimal>("Strategies:CallCreditSpread:MinDelta", 0.05m);
            var maxDelta = _configuration.GetValue<decimal>("Strategies:CallCreditSpread:MaxDelta", 0.15m);
            var minPremium = _configuration.GetValue<decimal>("Strategies:CallCreditSpread:MinPremium", 0.10m);

            // Find calls with target delta range
            var suitableCalls = chain.Calls.Where(c =>
                c.Delta >= minDelta &&
                c.Delta <= maxDelta &&
                c.IsLiquid &&
                c.StrikePrice > chain.UnderlyingPrice &&
                c.MidPrice >= minPremium).ToList();

            foreach (var shortCall in suitableCalls)
            {
                var longCallStrike = shortCall.StrikePrice + maxSpreadWidth;
                var longCall = chain.GetCallByStrike(longCallStrike);

                if (longCall != null && longCall.IsLiquid)
                {
                    var credit = shortCall.MidPrice - longCall.MidPrice;
                    var maxLoss = maxSpreadWidth - credit;
                    var profitTarget = _configuration.GetValue<decimal>("Strategies:CallCreditSpread:ProfitTarget", 0.5m);

                    if (credit >= minPremium && maxLoss > 0)
                    {
                        var signal = new TradingSignal
                        {
                            Strategy = "CallCreditSpread",
                            UnderlyingSymbol = chain.UnderlyingSymbol,
                            Type = SignalType.CallSpread,
                            ExpirationDate = chain.ExpirationDate,
                            ExpectedProfit = credit * profitTarget,
                            MaxLoss = maxLoss,
                            Confidence = CalculateCallCreditSpreadConfidence(shortCall, longCall, credit, chain),
                            Reason = $"Call Credit Spread: {shortCall.StrikePrice}/{longCall.StrikePrice}, Credit={credit:C2}, Delta={shortCall.Delta:F2}",
                            Legs = new List<OptionLeg>
                            {
                                new() { Symbol = shortCall.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Call, StrikePrice = shortCall.StrikePrice, Side = OrderSide.Sell, Quantity = 1, Price = shortCall.MidPrice, Delta = shortCall.Delta },
                                new() { Symbol = longCall.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Call, StrikePrice = longCall.StrikePrice, Side = OrderSide.Buy, Quantity = 1, Price = longCall.MidPrice, Delta = longCall.Delta }
                            }
                        };

                        signals.Add(signal);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error finding call credit spread opportunities for {chain.UnderlyingSymbol}");
        }

        return signals.Take(2).ToList();
    }

    public async Task<List<TradingSignal>> FindIronButterflyOpportunitiesAsync(OptionChain chain)
    {
        var signals = new List<TradingSignal>();

        if (!_configuration.GetValue<bool>("Strategies:IronButterfly:Enabled", true))
            return signals;

        try
        {
            var wingWidth = _configuration.GetValue<decimal>("Strategies:IronButterfly:WingWidth", 25);
            var minPremium = _configuration.GetValue<decimal>("Strategies:IronButterfly:MinPremium", 0.15m);
            var atmRange = _configuration.GetValue<decimal>("Strategies:IronButterfly:ATMRange", 0.02m);

            // Find ATM strike (round to nearest $5 for SPX, $1 for SPY)
            var strikeIncrement = chain.UnderlyingSymbol == "SPX" ? 5m : 1m;
            var atmStrike = Math.Round(chain.UnderlyingPrice / strikeIncrement) * strikeIncrement;

            // Iron Butterfly: Sell ATM straddle, buy wings
            var atmCall = chain.GetCallByStrike(atmStrike);
            var atmPut = chain.GetPutByStrike(atmStrike);
            var longCall = chain.GetCallByStrike(atmStrike + wingWidth);
            var longPut = chain.GetPutByStrike(atmStrike - wingWidth);

            if (atmCall != null && atmPut != null && longCall != null && longPut != null &&
                atmCall.IsLiquid && atmPut.IsLiquid && longCall.IsLiquid && longPut.IsLiquid)
            {
                var credit = (atmCall.MidPrice + atmPut.MidPrice) - (longCall.MidPrice + longPut.MidPrice);
                var maxLoss = wingWidth - credit;
                var profitTarget = _configuration.GetValue<decimal>("Strategies:IronButterfly:ProfitTarget", 0.5m);

                if (credit >= minPremium && maxLoss > 0)
                {
                    var signal = new TradingSignal
                    {
                        Strategy = "IronButterfly",
                        UnderlyingSymbol = chain.UnderlyingSymbol,
                        Type = SignalType.IronButterfly,
                        ExpirationDate = chain.ExpirationDate,
                        ExpectedProfit = credit * profitTarget,
                        MaxLoss = maxLoss,
                        Confidence = CalculateIronButterflyConfidence(atmCall, atmPut, credit, chain),
                        Reason = $"Iron Butterfly: ATM={atmStrike}, Credit={credit:C2}, Wings=±{wingWidth}",
                        Legs = new List<OptionLeg>
                        {
                            new() { Symbol = atmCall.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Call, StrikePrice = atmStrike, Side = OrderSide.Sell, Quantity = 1, Price = atmCall.MidPrice },
                            new() { Symbol = atmPut.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Put, StrikePrice = atmStrike, Side = OrderSide.Sell, Quantity = 1, Price = atmPut.MidPrice },
                            new() { Symbol = longCall.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Call, StrikePrice = atmStrike + wingWidth, Side = OrderSide.Buy, Quantity = 1, Price = longCall.MidPrice },
                            new() { Symbol = longPut.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Put, StrikePrice = atmStrike - wingWidth, Side = OrderSide.Buy, Quantity = 1, Price = longPut.MidPrice }
                        }
                    };

                    signals.Add(signal);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error finding iron butterfly opportunities for {chain.UnderlyingSymbol}");
        }

        return signals.Take(1).ToList(); // Only one iron butterfly per underlying
    }

    public async Task<List<TradingSignal>> FindIronCondorOpportunitiesAsync(OptionChain chain)
    {
        var signals = new List<TradingSignal>();

        if (!_configuration.GetValue<bool>("Strategies:IronCondor:Enabled", true))
            return signals;

        try
        {
            var wingWidth = _configuration.GetValue<decimal>("Strategies:IronCondor:WingWidth", 15);
            var minPremium = _configuration.GetValue<decimal>("Strategies:IronCondor:MinPremium", 0.30m);
            var minDelta = _configuration.GetValue<decimal>("Strategies:IronCondor:MinDelta", 0.05m);
            var maxDelta = _configuration.GetValue<decimal>("Strategies:IronCondor:MaxDelta", 0.10m);

            // Calculate expected move for strike placement
            var expectedMove = await CalculateExpectedMoveAsync(chain.UnderlyingSymbol, chain.ExpirationDate);

            // Place strikes outside expected move for higher win rate
            var callStrike = chain.UnderlyingPrice + (expectedMove * 1.2m);
            var putStrike = chain.UnderlyingPrice - (expectedMove * 1.2m);

            // Round to nearest strike increment
            var strikeIncrement = chain.UnderlyingSymbol == "SPX" ? 5m : 1m;
            callStrike = Math.Round(callStrike / strikeIncrement) * strikeIncrement;
            putStrike = Math.Round(putStrike / strikeIncrement) * strikeIncrement;

            // Find options with target delta range
            var shortCall = chain.Calls.FirstOrDefault(c =>
                Math.Abs(c.StrikePrice - callStrike) <= strikeIncrement * 2 &&
                c.Delta >= minDelta && c.Delta <= maxDelta && c.IsLiquid);

            var shortPut = chain.Puts.FirstOrDefault(p =>
                Math.Abs(p.StrikePrice - putStrike) <= strikeIncrement * 2 &&
                Math.Abs(p.Delta) >= minDelta && Math.Abs(p.Delta) <= maxDelta && p.IsLiquid);

            if (shortCall != null && shortPut != null)
            {
                // Find long options for wings
                var longCall = chain.GetCallByStrike(shortCall.StrikePrice + wingWidth);
                var longPut = chain.GetPutByStrike(shortPut.StrikePrice - wingWidth);

                if (longCall != null && longPut != null && longCall.IsLiquid && longPut.IsLiquid)
                {
                    var totalCredit = (shortCall.MidPrice + shortPut.MidPrice) - (longCall.MidPrice + longPut.MidPrice);
                    var maxLoss = wingWidth - totalCredit;
                    var profitTarget = _configuration.GetValue<decimal>("Strategies:IronCondor:ProfitTarget", 0.50m);

                    if (totalCredit >= minPremium && maxLoss > 0)
                    {
                        var signal = new TradingSignal
                        {
                            Strategy = "IronCondor",
                            UnderlyingSymbol = chain.UnderlyingSymbol,
                            Type = SignalType.IronCondor,
                            ExpirationDate = chain.ExpirationDate,
                            ExpectedProfit = totalCredit * profitTarget,
                            MaxLoss = maxLoss,
                            Confidence = CalculateIronCondorConfidence(shortCall, shortPut, totalCredit, chain, expectedMove),
                            Reason = $"Iron Condor: {shortPut.StrikePrice}/{shortCall.StrikePrice} (±{wingWidth}), Credit={totalCredit:C2}, ExpMove={expectedMove:F0}",
                            Legs = new List<OptionLeg>
                            {
                                new() { Symbol = shortCall.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Call, StrikePrice = shortCall.StrikePrice, Side = OrderSide.Sell, Quantity = 1, Price = shortCall.MidPrice, Delta = shortCall.Delta },
                                new() { Symbol = longCall.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Call, StrikePrice = longCall.StrikePrice, Side = OrderSide.Buy, Quantity = 1, Price = longCall.MidPrice, Delta = longCall.Delta },
                                new() { Symbol = shortPut.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Put, StrikePrice = shortPut.StrikePrice, Side = OrderSide.Sell, Quantity = 1, Price = shortPut.MidPrice, Delta = shortPut.Delta },
                                new() { Symbol = longPut.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Put, StrikePrice = longPut.StrikePrice, Side = OrderSide.Buy, Quantity = 1, Price = longPut.MidPrice, Delta = longPut.Delta }
                            }
                        };

                        signals.Add(signal);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error finding iron condor opportunities for {chain.UnderlyingSymbol}");
        }

        return signals.Take(1).ToList(); // Only one iron condor per underlying
    }

    public async Task<List<TradingSignal>> FindBrokenWingButterflyOpportunitiesAsync(OptionChain chain)
    {
        var signals = new List<TradingSignal>();

        if (!_configuration.GetValue<bool>("Strategies:BrokenWingButterfly:Enabled", true))
            return signals;

        try
        {
            var minPremium = _configuration.GetValue<decimal>("Strategies:BrokenWingButterfly:MinPremium", 0.50m);

            // Get market regime to determine directional bias
            var regime = await _marketRegimeAnalyzer.GetCurrentRegimeAsync();
            var trend = regime.Trend;

            // Only trade broken wing butterflies with clear directional bias
            if (trend == MarketTrend.Neutral || regime.VolatilityRegime == VolatilityRegime.High)
            {
                return signals; // Skip in neutral or high volatility markets
            }

            var strikeIncrement = chain.UnderlyingSymbol == "SPX" ? 5m : 1m;
            var atmStrike = Math.Round(chain.UnderlyingPrice / strikeIncrement) * strikeIncrement;

            if (trend == MarketTrend.Bullish)
            {
                // Bullish Broken Wing Butterfly (Put side)
                // Sell 2 ATM puts, buy 1 ITM put, buy 1 OTM put (wider wing)
                var atmPut = chain.GetPutByStrike(atmStrike);
                var itmPut = chain.GetPutByStrike(atmStrike + (10 * strikeIncrement)); // ITM
                var otmPut = chain.GetPutByStrike(atmStrike - (25 * strikeIncrement)); // Wider OTM wing

                if (atmPut != null && itmPut != null && otmPut != null &&
                    atmPut.IsLiquid && itmPut.IsLiquid && otmPut.IsLiquid)
                {
                    var netCredit = (atmPut.MidPrice * 2) - itmPut.MidPrice - otmPut.MidPrice;
                    var maxLoss = Math.Max(
                        Math.Abs(atmStrike - itmPut.StrikePrice) - netCredit,
                        Math.Abs(atmStrike - otmPut.StrikePrice) - netCredit);

                    if (netCredit >= minPremium && maxLoss > 0)
                    {
                        var signal = new TradingSignal
                        {
                            Strategy = "BrokenWingButterfly",
                            UnderlyingSymbol = chain.UnderlyingSymbol,
                            Type = SignalType.IronButterfly, // Reuse existing type
                            ExpirationDate = chain.ExpirationDate,
                            ExpectedProfit = netCredit * 0.60m, // Higher profit target for BWB
                            MaxLoss = maxLoss,
                            Confidence = CalculateBrokenWingButterflyConfidence(atmPut, netCredit, chain, "Bullish"),
                            Reason = $"Bullish BWB: {itmPut.StrikePrice}/{atmStrike}x2/{otmPut.StrikePrice}, Credit={netCredit:C2}, Trend={trend}",
                            Legs = new List<OptionLeg>
                            {
                                new() { Symbol = atmPut.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Put, StrikePrice = atmStrike, Side = OrderSide.Sell, Quantity = 2, Price = atmPut.MidPrice, Delta = atmPut.Delta },
                                new() { Symbol = itmPut.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Put, StrikePrice = itmPut.StrikePrice, Side = OrderSide.Buy, Quantity = 1, Price = itmPut.MidPrice, Delta = itmPut.Delta },
                                new() { Symbol = otmPut.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Put, StrikePrice = otmPut.StrikePrice, Side = OrderSide.Buy, Quantity = 1, Price = otmPut.MidPrice, Delta = otmPut.Delta }
                            }
                        };

                        signals.Add(signal);
                    }
                }
            }
            else if (trend == MarketTrend.Bearish)
            {
                // Bearish Broken Wing Butterfly (Call side)
                // Sell 2 ATM calls, buy 1 ITM call, buy 1 OTM call (wider wing)
                var atmCall = chain.GetCallByStrike(atmStrike);
                var itmCall = chain.GetCallByStrike(atmStrike - (10 * strikeIncrement)); // ITM
                var otmCall = chain.GetCallByStrike(atmStrike + (25 * strikeIncrement)); // Wider OTM wing

                if (atmCall != null && itmCall != null && otmCall != null &&
                    atmCall.IsLiquid && itmCall.IsLiquid && otmCall.IsLiquid)
                {
                    var netCredit = (atmCall.MidPrice * 2) - itmCall.MidPrice - otmCall.MidPrice;
                    var maxLoss = Math.Max(
                        Math.Abs(itmCall.StrikePrice - atmStrike) - netCredit,
                        Math.Abs(otmCall.StrikePrice - atmStrike) - netCredit);

                    if (netCredit >= minPremium && maxLoss > 0)
                    {
                        var signal = new TradingSignal
                        {
                            Strategy = "BrokenWingButterfly",
                            UnderlyingSymbol = chain.UnderlyingSymbol,
                            Type = SignalType.IronButterfly, // Reuse existing type
                            ExpirationDate = chain.ExpirationDate,
                            ExpectedProfit = netCredit * 0.60m,
                            MaxLoss = maxLoss,
                            Confidence = CalculateBrokenWingButterflyConfidence(atmCall, netCredit, chain, "Bearish"),
                            Reason = $"Bearish BWB: {itmCall.StrikePrice}/{atmStrike}x2/{otmCall.StrikePrice}, Credit={netCredit:C2}, Trend={trend}",
                            Legs = new List<OptionLeg>
                            {
                                new() { Symbol = atmCall.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Call, StrikePrice = atmStrike, Side = OrderSide.Sell, Quantity = 2, Price = atmCall.MidPrice, Delta = atmCall.Delta },
                                new() { Symbol = itmCall.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Call, StrikePrice = itmCall.StrikePrice, Side = OrderSide.Buy, Quantity = 1, Price = itmCall.MidPrice, Delta = itmCall.Delta },
                                new() { Symbol = otmCall.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Call, StrikePrice = otmCall.StrikePrice, Side = OrderSide.Buy, Quantity = 1, Price = otmCall.MidPrice, Delta = otmCall.Delta }
                            }
                        };

                        signals.Add(signal);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error finding broken wing butterfly opportunities for {chain.UnderlyingSymbol}");
        }

        return signals.Take(1).ToList(); // Only one broken wing butterfly per underlying
    }

    private int GetStrategyPriority(string strategy)
    {
        return strategy switch
        {
            "PutCreditSpread" => 1,
            "IronCondor" => 2,        // High win rate strategy
            "CallCreditSpread" => 3,
            "BrokenWingButterfly" => 4, // Directional bias strategy
            "IronButterfly" => 5,
            _ => 99
        };
    }

    private decimal CalculatePutCreditSpreadConfidence(OptionContract shortPut, OptionContract longPut, decimal credit, OptionChain chain)
    {
        try
        {
            // Risk/reward ratio (higher is better)
            var maxLoss = Math.Abs(shortPut.StrikePrice - longPut.StrikePrice) - credit;
            var riskReward = maxLoss > 0 ? credit / maxLoss : 0;

            // Liquidity score (higher volume = better)
            var liquidityScore = Math.Min(1.0m, (shortPut.Volume + longPut.Volume) / 50);

            // Delta appropriateness (closer to target delta = better)
            var targetDelta = 0.10m;
            var deltaScore = 1 - Math.Abs(Math.Abs(shortPut.Delta) - targetDelta) / targetDelta;

            // Distance from current price (further OTM = safer)
            var distanceScore = Math.Min(1.0m, (chain.UnderlyingPrice - shortPut.StrikePrice) / chain.UnderlyingPrice * 10);

            // Time decay benefit (always high for 0 DTE)
            var timeDecayScore = 0.9m;

            var confidence = (riskReward * 0.25m) + (liquidityScore * 0.20m) + (deltaScore * 0.25m) +
                           (distanceScore * 0.15m) + (timeDecayScore * 0.15m);

            return Math.Min(0.95m, Math.Max(0.1m, confidence));
        }
        catch
        {
            return 0.5m; // Default confidence
        }
    }

    private decimal CalculateCallCreditSpreadConfidence(OptionContract shortCall, OptionContract longCall, decimal credit, OptionChain chain)
    {
        try
        {
            var maxLoss = Math.Abs(longCall.StrikePrice - shortCall.StrikePrice) - credit;
            var riskReward = maxLoss > 0 ? credit / maxLoss : 0;
            var liquidityScore = Math.Min(1.0m, (shortCall.Volume + longCall.Volume) / 50);
            var targetDelta = 0.10m;
            var deltaScore = 1 - Math.Abs(shortCall.Delta - targetDelta) / targetDelta;
            var distanceScore = Math.Min(1.0m, (shortCall.StrikePrice - chain.UnderlyingPrice) / chain.UnderlyingPrice * 10);
            var timeDecayScore = 0.9m;

            var confidence = (riskReward * 0.25m) + (liquidityScore * 0.20m) + (deltaScore * 0.25m) +
                           (distanceScore * 0.15m) + (timeDecayScore * 0.15m);

            return Math.Min(0.95m, Math.Max(0.1m, confidence));
        }
        catch
        {
            return 0.5m;
        }
    }

    private decimal CalculateIronButterflyConfidence(OptionContract atmCall, OptionContract atmPut, decimal credit, OptionChain chain)
    {
        try
        {
            // Credit quality (higher credit = better, but not too high)
            var creditScore = credit >= 0.50m && credit <= 1.50m ? 0.9m : 0.5m;

            // ATM liquidity (critical for iron butterfly)
            var liquidityScore = Math.Min(1.0m, (atmCall.Volume + atmPut.Volume) / 100);

            // Volatility appropriateness (lower IV = better for selling premium)
            var ivScore = atmCall.ImpliedVolatility > 0 ? Math.Max(0.3m, 1 - (atmCall.ImpliedVolatility / 0.5m)) : 0.7m;

            // Time decay benefit
            var timeDecayScore = 0.95m; // Very high for 0 DTE

            // Market neutrality benefit (works best in range-bound markets)
            var neutralityScore = 0.8m;

            var confidence = (creditScore * 0.25m) + (liquidityScore * 0.25m) + (ivScore * 0.20m) +
                           (timeDecayScore * 0.15m) + (neutralityScore * 0.15m);

            return Math.Min(0.95m, Math.Max(0.1m, confidence));
        }
        catch
        {
            return 0.6m;
        }
    }

    private decimal CalculateIronCondorConfidence(OptionContract shortCall, OptionContract shortPut, decimal credit, OptionChain chain, decimal expectedMove)
    {
        try
        {
            // Credit quality (higher credit = better for iron condors)
            var creditScore = credit >= 0.30m && credit <= 1.00m ? 0.9m : 0.6m;

            // Strike placement relative to expected move (further = better)
            var callDistance = shortCall.StrikePrice - chain.UnderlyingPrice;
            var putDistance = chain.UnderlyingPrice - shortPut.StrikePrice;
            var avgDistance = (callDistance + putDistance) / 2;
            var distanceScore = Math.Min(1.0m, avgDistance / expectedMove);

            // Liquidity score
            var liquidityScore = Math.Min(1.0m, (shortCall.Volume + shortPut.Volume) / 100);

            // Low volatility preference (iron condors work best in low vol)
            var volScore = shortCall.ImpliedVolatility > 0 ? Math.Max(0.4m, 1 - (shortCall.ImpliedVolatility / 0.3m)) : 0.8m;

            // Time decay benefit
            var timeDecayScore = 0.95m; // Very high for 0 DTE

            var confidence = (creditScore * 0.25m) + (distanceScore * 0.25m) + (liquidityScore * 0.20m) +
                           (volScore * 0.15m) + (timeDecayScore * 0.15m);

            return Math.Min(0.95m, Math.Max(0.1m, confidence));
        }
        catch
        {
            return 0.7m; // Default confidence for iron condors
        }
    }

    private decimal CalculateBrokenWingButterflyConfidence(OptionContract atmOption, decimal credit, OptionChain chain, string direction)
    {
        try
        {
            // Credit quality (BWB should generate good credit)
            var creditScore = credit >= 0.50m ? 0.9m : 0.5m;

            // Directional alignment with market trend
            var directionScore = 0.8m; // Already filtered by trend in calling method

            // Liquidity score (ATM options usually have good liquidity)
            var liquidityScore = Math.Min(1.0m, atmOption.Volume / 100);

            // Volatility appropriateness (moderate vol is best)
            var volScore = atmOption.ImpliedVolatility > 0 ?
                Math.Max(0.5m, 1 - Math.Abs(atmOption.ImpliedVolatility - 0.25m) / 0.25m) : 0.7m;

            // Time decay benefit (good for 0 DTE)
            var timeDecayScore = 0.85m;

            var confidence = (creditScore * 0.30m) + (directionScore * 0.25m) + (liquidityScore * 0.20m) +
                           (volScore * 0.15m) + (timeDecayScore * 0.10m);

            return Math.Min(0.90m, Math.Max(0.1m, confidence));
        }
        catch
        {
            return 0.6m; // Default confidence for broken wing butterflies
        }
    }

    // Enhanced Signal Quality Implementation
    public async Task<decimal> CalculateSignalQualityScoreAsync(TradingSignal signal, OptionChain chain)
    {
        try
        {
            _logger.LogDebug($"Calculating signal quality score for {signal.Strategy} on {signal.UnderlyingSymbol}");

            var qualityFactors = new Dictionary<string, decimal>();

            // Factor 1: Liquidity Score (25% weight)
            var liquidityScore = CalculateLiquidityScore(signal, chain);
            qualityFactors["Liquidity"] = liquidityScore * 0.25m;

            // Factor 2: Risk-Reward Ratio (20% weight)
            var riskRewardScore = CalculateRiskRewardScore(signal);
            qualityFactors["RiskReward"] = riskRewardScore * 0.20m;

            // Factor 3: Market Regime Alignment (20% weight)
            var regimeScore = await CalculateRegimeAlignmentScore(signal);
            qualityFactors["RegimeAlignment"] = regimeScore * 0.20m;

            // Factor 4: Technical Setup Quality (15% weight)
            var technicalScore = await CalculateTechnicalSetupScore(signal, chain);
            qualityFactors["TechnicalSetup"] = technicalScore * 0.15m;

            // Factor 5: Volatility Environment (10% weight)
            var volatilityScore = await CalculateVolatilityScore(signal, chain);
            qualityFactors["Volatility"] = volatilityScore * 0.10m;

            // Factor 6: Time Decay Favorability (10% weight)
            var timeDecayScore = CalculateTimeDecayScore(signal);
            qualityFactors["TimeDecay"] = timeDecayScore * 0.10m;

            var totalScore = qualityFactors.Values.Sum();

            // Store quality breakdown in signal parameters
            signal.Parameters["QualityBreakdown"] = qualityFactors;
            signal.Parameters["QualityScore"] = totalScore;

            _logger.LogDebug($"Signal quality score for {signal.Id}: {totalScore:F3} (Liquidity: {liquidityScore:F2}, RiskReward: {riskRewardScore:F2}, Regime: {regimeScore:F2})");

            return Math.Max(0, Math.Min(1, totalScore));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating signal quality score for {signal.Id}");
            return 0.5m; // Default neutral score
        }
    }

    public async Task<List<TradingSignal>> RankSignalsByQualityAsync(List<TradingSignal> signals, List<OptionChain> chains)
    {
        try
        {
            _logger.LogInformation($"Ranking {signals.Count} signals by quality");

            var scoredSignals = new List<(TradingSignal Signal, decimal QualityScore)>();

            foreach (var signal in signals)
            {
                var chain = chains.FirstOrDefault(c => c.UnderlyingSymbol == signal.UnderlyingSymbol);
                if (chain != null)
                {
                    var qualityScore = await CalculateSignalQualityScoreAsync(signal, chain);
                    scoredSignals.Add((signal, qualityScore));
                }
                else
                {
                    scoredSignals.Add((signal, 0.3m)); // Low score if no chain data
                }
            }

            // Sort by quality score (descending) and return signals
            var rankedSignals = scoredSignals
                .OrderByDescending(s => s.QualityScore)
                .ThenByDescending(s => s.Signal.Confidence)
                .ThenByDescending(s => s.Signal.RiskRewardRatio)
                .Select(s => s.Signal)
                .ToList();

            _logger.LogInformation($"Signal ranking completed. Top signal: {rankedSignals.FirstOrDefault()?.Strategy} with quality score: {scoredSignals.FirstOrDefault().QualityScore:F3}");

            return rankedSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ranking signals by quality");
            return signals; // Return original list on error
        }
    }

    public async Task<SignalValidationResult> ValidateSignalQualityAsync(TradingSignal signal, OptionChain chain)
    {
        try
        {
            var validation = new SignalValidationResult
            {
                SignalId = signal.Id,
                IsValid = true,
                ValidationTimestamp = DateTime.UtcNow
            };

            var issues = new List<string>();
            var warnings = new List<string>();

            // Validate liquidity
            var liquidityScore = CalculateLiquidityScore(signal, chain);
            if (liquidityScore < 0.3m)
            {
                issues.Add($"Poor liquidity score: {liquidityScore:F2}");
                validation.IsValid = false;
            }
            else if (liquidityScore < 0.6m)
            {
                warnings.Add($"Moderate liquidity concern: {liquidityScore:F2}");
            }

            // Validate risk-reward
            if (signal.RiskRewardRatio < 0.1m)
            {
                issues.Add($"Poor risk-reward ratio: {signal.RiskRewardRatio:F2}");
                validation.IsValid = false;
            }

            // Validate market regime alignment
            var regimeScore = await CalculateRegimeAlignmentScore(signal);
            if (regimeScore < 0.4m)
            {
                warnings.Add($"Poor market regime alignment: {regimeScore:F2}");
            }

            // Validate option Greeks
            foreach (var leg in signal.Legs)
            {
                if (Math.Abs(leg.Delta) > 0.3m && signal.Strategy.Contains("Credit"))
                {
                    warnings.Add($"High delta exposure in credit strategy: {leg.Delta:F3}");
                }
            }

            // Calculate overall quality score
            validation.QualityScore = await CalculateSignalQualityScoreAsync(signal, chain);
            validation.Issues = issues;
            validation.Warnings = warnings;

            if (validation.QualityScore < 0.3m)
            {
                validation.IsValid = false;
                issues.Add($"Overall quality score too low: {validation.QualityScore:F3}");
            }

            _logger.LogDebug($"Signal validation for {signal.Id}: Valid={validation.IsValid}, Quality={validation.QualityScore:F3}, Issues={issues.Count}, Warnings={warnings.Count}");

            return validation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error validating signal quality for {signal.Id}");
            return new SignalValidationResult
            {
                SignalId = signal.Id,
                IsValid = false,
                Issues = new List<string> { "Validation error occurred" },
                ValidationTimestamp = DateTime.UtcNow
            };
        }
    }

    private decimal CalculateLiquidityScore(TradingSignal signal, OptionChain chain)
    {
        try
        {
            var liquidityScores = new List<decimal>();

            foreach (var leg in signal.Legs)
            {
                var option = leg.OptionType == OptionType.Call
                    ? chain.GetCallByStrike(leg.StrikePrice)
                    : chain.GetPutByStrike(leg.StrikePrice);

                if (option != null)
                {
                    // Score based on volume, open interest, and spread
                    var volumeScore = Math.Min(1m, option.Volume / 100m); // Normalize to 100 volume
                    var oiScore = Math.Min(1m, option.OpenInterest / 500m); // Normalize to 500 OI
                    var spreadScore = option.SpreadPercentage < 5 ? 1m :
                                    option.SpreadPercentage < 10 ? 0.7m :
                                    option.SpreadPercentage < 20 ? 0.4m : 0.1m;

                    var legLiquidity = (volumeScore * 0.4m) + (oiScore * 0.4m) + (spreadScore * 0.2m);
                    liquidityScores.Add(legLiquidity);
                }
                else
                {
                    liquidityScores.Add(0.1m); // Very low score for missing option
                }
            }

            return liquidityScores.Any() ? liquidityScores.Average() : 0.1m;
        }
        catch
        {
            return 0.3m; // Default moderate score on error
        }
    }

    private decimal CalculateRiskRewardScore(TradingSignal signal)
    {
        try
        {
            // Score based on risk-reward ratio with 0 DTE adjustments
            var ratio = signal.RiskRewardRatio;

            if (ratio >= 0.3m) return 1.0m;      // Excellent
            if (ratio >= 0.2m) return 0.8m;      // Good
            if (ratio >= 0.15m) return 0.6m;     // Acceptable
            if (ratio >= 0.1m) return 0.4m;      // Poor
            return 0.1m;                          // Very poor
        }
        catch
        {
            return 0.3m;
        }
    }

    private async Task<decimal> CalculateRegimeAlignmentScore(TradingSignal signal)
    {
        try
        {
            var currentRegime = await _marketRegimeAnalyzer.GetCurrentRegimeAsync();
            var recommendedStrategies = await _marketRegimeAnalyzer.GetRecommendedStrategiesAsync();

            // Check if signal strategy is recommended for current regime
            if (recommendedStrategies.Contains(signal.Strategy))
            {
                // Higher score for better regime alignment
                return currentRegime.VolatilityRegime switch
                {
                    VolatilityRegime.Low => signal.Strategy == "PutCreditSpread" ? 0.9m : 0.8m,
                    VolatilityRegime.Medium => 0.7m,
                    VolatilityRegime.High => 0.3m, // Generally avoid trading in high vol
                    _ => 0.5m
                };
            }

            return 0.2m; // Low score if strategy not recommended
        }
        catch
        {
            return 0.5m;
        }
    }

    private async Task<decimal> CalculateTechnicalSetupScore(TradingSignal signal, OptionChain chain)
    {
        try
        {
            var microstructure = await _marketRegimeAnalyzer.GetMarketMicrostructureAsync(signal.UnderlyingSymbol);
            var multiTimeframe = await _marketRegimeAnalyzer.GetMultiTimeframeAnalysisAsync(signal.UnderlyingSymbol);

            var scores = new List<decimal>();

            // Momentum alignment score
            var momentumScore = Math.Max(0, Math.Min(1, (microstructure.MomentumScore + 50) / 100)); // Normalize -50 to +50 range
            scores.Add(momentumScore);

            // Liquidity score from microstructure
            var liquidityScore = microstructure.LiquidityScore / 100m;
            scores.Add(liquidityScore);

            // Multi-timeframe alignment
            var alignmentScore = multiTimeframe.TrendAlignment;
            scores.Add(alignmentScore);

            // Technical indicators
            if (microstructure.TechnicalIndicators.ContainsKey("RSI"))
            {
                var rsi = microstructure.TechnicalIndicators["RSI"];
                var rsiScore = signal.Strategy.Contains("Credit") ?
                    (rsi > 30 && rsi < 70 ? 0.8m : 0.4m) : // Credit spreads prefer neutral RSI
                    (rsi < 30 || rsi > 70 ? 0.8m : 0.4m);   // Other strategies prefer extreme RSI
                scores.Add(rsiScore);
            }

            return scores.Any() ? scores.Average() : 0.5m;
        }
        catch
        {
            return 0.5m;
        }
    }

    private async Task<decimal> CalculateVolatilityScore(TradingSignal signal, OptionChain chain)
    {
        try
        {
            var forecast = await _marketRegimeAnalyzer.GetVolatilityForecastAsync(signal.UnderlyingSymbol);

            // For credit strategies, lower volatility is better
            if (signal.Strategy.Contains("Credit"))
            {
                if (forecast.ForecastedVolatility < 15) return 0.9m;      // Very low vol - excellent
                if (forecast.ForecastedVolatility < 20) return 0.8m;      // Low vol - good
                if (forecast.ForecastedVolatility < 25) return 0.6m;      // Medium vol - acceptable
                if (forecast.ForecastedVolatility < 30) return 0.4m;      // High vol - poor
                return 0.2m;                                               // Very high vol - very poor
            }

            // For other strategies, moderate volatility might be preferred
            if (forecast.ForecastedVolatility >= 15 && forecast.ForecastedVolatility <= 25)
                return 0.8m;

            return 0.5m; // Default moderate score
        }
        catch
        {
            return 0.5m;
        }
    }

    private decimal CalculateTimeDecayScore(TradingSignal signal)
    {
        try
        {
            // For 0 DTE options, time decay is always favorable for sellers
            if (signal.Strategy.Contains("Credit") || signal.Strategy.Contains("Butterfly"))
            {
                return 0.95m; // Very high score for premium selling strategies
            }

            // For buying strategies, time decay is unfavorable
            return 0.3m;
        }
        catch
        {
            return 0.5m;
        }
    }

    // Enhanced Options Pricing and Greeks Implementation
    public async Task<decimal> CalculateImpliedVolatilityAsync(OptionContract option, decimal underlyingPrice)
    {
        try
        {
            // Use Newton-Raphson method to solve for implied volatility
            var timeToExpiry = (decimal)(option.ExpirationDate - DateTime.Now).TotalDays / 365m;
            if (timeToExpiry <= 0) return 0;

            var riskFreeRate = 0.05m; // 5% risk-free rate
            var targetPrice = option.MidPrice;
            var volatility = 0.20m; // Initial guess

            for (int i = 0; i < 100; i++) // Max 100 iterations
            {
                var theoreticalPrice = CalculateBlackScholesPrice(underlyingPrice, option.StrikePrice,
                    timeToExpiry, riskFreeRate, volatility, option.OptionType);

                var vega = CalculateVega(underlyingPrice, option.StrikePrice, timeToExpiry, riskFreeRate, volatility);

                if (Math.Abs(theoreticalPrice - targetPrice) < 0.001m || vega == 0) break;

                volatility = volatility - (theoreticalPrice - targetPrice) / vega;
                volatility = Math.Max(0.001m, Math.Min(5.0m, volatility)); // Bounds check
            }

            return volatility;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating implied volatility for {option.Symbol}");
            return 0.20m; // Default 20% volatility
        }
    }

    public async Task<OptionGreeks> CalculateGreeksAsync(OptionContract option, decimal underlyingPrice, decimal riskFreeRate = 0.05m)
    {
        try
        {
            var timeToExpiry = (decimal)(option.ExpirationDate - DateTime.Now).TotalDays / 365m;
            if (timeToExpiry <= 0)
                return new OptionGreeks { Delta = 0, Gamma = 0, Theta = 0, Vega = 0, Rho = 0 };

            var impliedVol = await CalculateImpliedVolatilityAsync(option, underlyingPrice);

            var greeks = new OptionGreeks
            {
                Delta = CalculateDelta(underlyingPrice, option.StrikePrice, timeToExpiry, riskFreeRate, impliedVol, option.OptionType),
                Gamma = CalculateGamma(underlyingPrice, option.StrikePrice, timeToExpiry, riskFreeRate, impliedVol),
                Theta = CalculateTheta(underlyingPrice, option.StrikePrice, timeToExpiry, riskFreeRate, impliedVol, option.OptionType),
                Vega = CalculateVega(underlyingPrice, option.StrikePrice, timeToExpiry, riskFreeRate, impliedVol),
                Rho = CalculateRho(underlyingPrice, option.StrikePrice, timeToExpiry, riskFreeRate, impliedVol, option.OptionType)
            };

            return greeks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating Greeks for {option.Symbol}");
            return new OptionGreeks();
        }
    }

    public async Task<decimal> CalculateTheoreticalPriceAsync(OptionContract option, decimal underlyingPrice, decimal impliedVol)
    {
        try
        {
            var timeToExpiry = (decimal)(option.ExpirationDate - DateTime.Now).TotalDays / 365m;
            if (timeToExpiry <= 0) return 0;

            var riskFreeRate = 0.05m;
            return CalculateBlackScholesPrice(underlyingPrice, option.StrikePrice,
                timeToExpiry, riskFreeRate, impliedVol, option.OptionType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating theoretical price for {option.Symbol}");
            return option.MidPrice; // Fallback to market price
        }
    }

    public async Task<bool> IsOptionFairlyPricedAsync(OptionContract option, decimal underlyingPrice)
    {
        try
        {
            var impliedVol = await CalculateImpliedVolatilityAsync(option, underlyingPrice);
            var theoreticalPrice = await CalculateTheoreticalPriceAsync(option, underlyingPrice, impliedVol);

            var priceDifference = Math.Abs(option.MidPrice - theoreticalPrice);
            var tolerance = theoreticalPrice * 0.05m; // 5% tolerance

            return priceDifference <= tolerance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error checking fair pricing for {option.Symbol}");
            return true; // Assume fairly priced if calculation fails
        }
    }

    public async Task<decimal> CalculateExpectedMoveAsync(string symbol, DateTime expirationDate)
    {
        try
        {
            var timeToExpiry = (decimal)(expirationDate - DateTime.Now).TotalDays / 365m;
            if (timeToExpiry <= 0) return 0;

            var currentPrice = await _alpacaService.GetCurrentPriceAsync(symbol);
            var historicalVol = await _marketRegimeAnalyzer.GetVixAsync() / 100m; // Convert VIX to decimal

            // Expected move = Stock Price × Implied Volatility × √(Time to Expiration)
            var expectedMove = currentPrice * historicalVol * (decimal)Math.Sqrt((double)timeToExpiry);

            _logger.LogDebug($"Expected move for {symbol} until {expirationDate:yyyy-MM-dd}: ±{expectedMove:F2} ({expectedMove/currentPrice:P1})");

            return expectedMove;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating expected move for {symbol}");
            return 0;
        }
    }

    // Black-Scholes Helper Methods
    private decimal CalculateBlackScholesPrice(decimal S, decimal K, decimal T, decimal r, decimal sigma, OptionType optionType)
    {
        if (T <= 0) return Math.Max(0, optionType == OptionType.Call ? S - K : K - S);

        var d1 = (decimal)(Math.Log((double)(S / K)) + (double)(r + sigma * sigma / 2) * (double)T) / (decimal)((double)sigma * Math.Sqrt((double)T));
        var d2 = d1 - sigma * (decimal)Math.Sqrt((double)T);

        var Nd1 = CumulativeNormalDistribution((double)d1);
        var Nd2 = CumulativeNormalDistribution((double)d2);
        var NegD1 = CumulativeNormalDistribution(-(double)d1);
        var NegD2 = CumulativeNormalDistribution(-(double)d2);

        if (optionType == OptionType.Call)
        {
            return S * (decimal)Nd1 - K * (decimal)Math.Exp(-(double)r * (double)T) * (decimal)Nd2;
        }
        else
        {
            return K * (decimal)Math.Exp(-(double)r * (double)T) * (decimal)NegD2 - S * (decimal)NegD1;
        }
    }

    private decimal CalculateDelta(decimal S, decimal K, decimal T, decimal r, decimal sigma, OptionType optionType)
    {
        if (T <= 0) return optionType == OptionType.Call ? (S > K ? 1 : 0) : (S < K ? -1 : 0);

        var d1 = (decimal)(Math.Log((double)(S / K)) + (double)(r + sigma * sigma / 2) * (double)T) / (decimal)((double)sigma * Math.Sqrt((double)T));
        var Nd1 = CumulativeNormalDistribution((double)d1);

        return optionType == OptionType.Call ? (decimal)Nd1 : (decimal)Nd1 - 1;
    }

    private decimal CalculateGamma(decimal S, decimal K, decimal T, decimal r, decimal sigma)
    {
        if (T <= 0) return 0;

        var d1 = (decimal)(Math.Log((double)(S / K)) + (double)(r + sigma * sigma / 2) * (double)T) / (decimal)((double)sigma * Math.Sqrt((double)T));
        var phi_d1 = (decimal)(Math.Exp(-0.5 * (double)d1 * (double)d1) / Math.Sqrt(2 * Math.PI));

        return phi_d1 / (S * sigma * (decimal)Math.Sqrt((double)T));
    }

    private decimal CalculateTheta(decimal S, decimal K, decimal T, decimal r, decimal sigma, OptionType optionType)
    {
        if (T <= 0) return 0;

        var d1 = (decimal)(Math.Log((double)(S / K)) + (double)(r + sigma * sigma / 2) * (double)T) / (decimal)((double)sigma * Math.Sqrt((double)T));
        var d2 = d1 - sigma * (decimal)Math.Sqrt((double)T);
        var phi_d1 = (decimal)(Math.Exp(-0.5 * (double)d1 * (double)d1) / Math.Sqrt(2 * Math.PI));
        var Nd2 = CumulativeNormalDistribution((double)d2);
        var NegD2 = CumulativeNormalDistribution(-(double)d2);

        var term1 = -S * phi_d1 * sigma / (2 * (decimal)Math.Sqrt((double)T));
        var term2 = r * K * (decimal)Math.Exp(-(double)r * (double)T);

        if (optionType == OptionType.Call)
        {
            return (term1 - term2 * (decimal)Nd2) / 365; // Convert to daily theta
        }
        else
        {
            return (term1 + term2 * (decimal)NegD2) / 365; // Convert to daily theta
        }
    }

    private decimal CalculateVega(decimal S, decimal K, decimal T, decimal r, decimal sigma)
    {
        if (T <= 0) return 0;

        var d1 = (decimal)(Math.Log((double)(S / K)) + (double)(r + sigma * sigma / 2) * (double)T) / (decimal)((double)sigma * Math.Sqrt((double)T));
        var phi_d1 = (decimal)(Math.Exp(-0.5 * (double)d1 * (double)d1) / Math.Sqrt(2 * Math.PI));

        return S * phi_d1 * (decimal)Math.Sqrt((double)T) / 100; // Convert to 1% vol change
    }

    private decimal CalculateRho(decimal S, decimal K, decimal T, decimal r, decimal sigma, OptionType optionType)
    {
        if (T <= 0) return 0;

        var d2 = (decimal)(Math.Log((double)(S / K)) + (double)(r - sigma * sigma / 2) * (double)T) / (decimal)((double)sigma * Math.Sqrt((double)T));
        var Nd2 = CumulativeNormalDistribution((double)d2);
        var NegD2 = CumulativeNormalDistribution(-(double)d2);

        if (optionType == OptionType.Call)
        {
            return K * T * (decimal)Math.Exp(-(double)r * (double)T) * (decimal)Nd2 / 100; // Convert to 1% rate change
        }
        else
        {
            return -K * T * (decimal)Math.Exp(-(double)r * (double)T) * (decimal)NegD2 / 100; // Convert to 1% rate change
        }
    }

    private double CumulativeNormalDistribution(double x)
    {
        // Approximation of the cumulative normal distribution
        const double a1 = 0.254829592;
        const double a2 = -0.284496736;
        const double a3 = 1.421413741;
        const double a4 = -1.453152027;
        const double a5 = 1.061405429;
        const double p = 0.3275911;

        var sign = x < 0 ? -1 : 1;
        x = Math.Abs(x) / Math.Sqrt(2.0);

        var t = 1.0 / (1.0 + p * x);
        var y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.Exp(-x * x);

        return 0.5 * (1.0 + sign * y);
    }
}
